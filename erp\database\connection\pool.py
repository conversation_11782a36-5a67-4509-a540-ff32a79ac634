"""
Database connection pool management
"""

from contextlib import asynccontextmanager
from typing import Optional

import asyncpg

from ...config import config
from ...logging import get_logger


class ConnectionPool:
    """Database connection pool manager"""

    def __init__(self, db_name: Optional[str] = None):
        self.db_name = db_name or config.get("options", "db_name")
        self._pool: Optional[asyncpg.Pool] = None
        self.logger = get_logger(f"{__name__}.{self.db_name}")

    async def create_pool(self):
        """Create connection pool"""
        if self._pool is None:
            db_config = config.db_config.config.copy()
            pool_config = config.db_pool_config

            if self.db_name:
                db_config["database"] = self.db_name

            self.logger.debug(
                "Creating database pool for %s at %s:%s",
                db_config['database'], db_config['host'], db_config['port']
            )

            try:
                self._pool = await asyncpg.create_pool(
                    host=db_config["host"],
                    port=db_config["port"],
                    user=db_config["user"],
                    password=db_config["password"],
                    database=db_config["database"],
                    min_size=pool_config["min_size"],
                    max_size=pool_config["max_size"],
                )
                self.logger.info(
                    "Database pool created successfully for %s (min: %d, max: %d)",
                    db_config['database'], pool_config['min_size'], pool_config['max_size']
                )
            except Exception as e:
                self.logger.error(
                    "Database pool creation error for %s: %s", db_config['database'], e
                )
                raise

    async def close_pool(self):
        """Close connection pool"""
        if self._pool:
            self.logger.debug("Closing database pool for %s", self.db_name)
            await self._pool.close()
            self._pool = None
            self.logger.info("Database pool closed for %s", self.db_name)

    @asynccontextmanager
    async def acquire_connection(self):
        """Acquire connection from pool"""
        if not self._pool:
            await self.create_pool()

        # Reduce verbosity - only log at debug level if needed for troubleshooting
        async with self._pool.acquire() as connection:
            yield connection

    @property
    def pool(self) -> Optional[asyncpg.Pool]:
        """Get the underlying pool"""
        return self._pool

    @property
    def is_closed(self) -> bool:
        """Check if pool is closed"""
        return self._pool is None
