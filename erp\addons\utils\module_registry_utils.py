"""
Module registry utilities for addon installation

This module provides shared utility functions for managing addon state in the
ir.module.module table. These functions support the XML-based module registration
approach where module records are loaded via XML data files.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from ...logging import get_logger

logger = get_logger(__name__)


async def unregister_addon_from_module_table(db_manager, addon_name: str) -> bool:
    """
    Mark an addon as uninstalled in the ir.module.module table

    Args:
        db_manager: Database manager instance
        addon_name: Technical name of the addon

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        current_time = datetime.now()
        await db_manager.execute(
            """UPDATE ir_module_module SET
               state = $1, update_at = $2, action_at = $3
               WHERE name = $4""",
            "uninstalled",
            current_time,
            current_time,
            addon_name,
        )

        logger.info(
            f"✓ Addon '{addon_name}' marked as uninstalled in ir.module.module table"
        )
        return True

    except Exception as e:
        logger.error(
            f"Failed to unregister addon '{addon_name}' from ir.module.module: {e}"
        )
        return False


async def update_addon_state_in_module_table(
    db_manager, addon_name: str, state: str
) -> bool:
    """
    Update addon state in the ir.module.module table

    Args:
        db_manager: Database manager instance
        addon_name: Technical name of the addon
        state: New state ('installed', 'uninstalled', 'to_upgrade', etc.)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        current_time = datetime.now()
        await db_manager.execute(
            """UPDATE ir_module_module SET
               state = $1, update_at = $2, action_at = $3
               WHERE name = $4""",
            state,
            current_time,
            current_time,
            addon_name,
        )

        logger.debug(
            f"✓ Addon '{addon_name}' state updated to '{state}' in ir.module.module table"
        )
        return True

    except Exception as e:
        logger.error(f"Failed to update addon '{addon_name}' state to '{state}': {e}")
        return False


def read_manifest_file(manifest_path: str) -> Optional[Dict[str, Any]]:
    """
    Read and parse an addon manifest file

    Args:
        manifest_path: Path to the __manifest__.py file

    Returns:
        Dictionary containing manifest data, or None if failed
    """
    try:
        import ast

        with open(manifest_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Parse the manifest as Python code
        manifest_data = ast.literal_eval(content)

        if not isinstance(manifest_data, dict):
            logger.error(f"Manifest file {manifest_path} does not contain a dictionary")
            return None

        return manifest_data

    except Exception as e:
        logger.error(f"Failed to read manifest from {manifest_path}: {e}")
        return None


async def populate_ir_module_table_with_all_addons(db_manager) -> Dict[str, Any]:
    """
    Populate the ir.module.module table with all valid addons from all addon paths.

    This function discovers all valid addons from all configured addon paths,
    reads their manifest files, and inserts/updates records in the ir.module.module
    table with default state 'uninstalled' (except for base which is set to 'installed').
    Uses upsert approach - updates existing records or inserts new ones.

    Args:
        db_manager: Database manager instance

    Returns:
        Dictionary with population results including counts and any errors
    """
    try:
        # Import here to avoid circular imports
        from .path_resolver import list_all_addons
        import os

        logger.info("Starting population of ir.module.module table with all discovered addons")

        # Discover all addons from all configured paths
        all_addons = list_all_addons()

        if not all_addons:
            logger.warning("No addons discovered from any configured addon path")
            return {
                "status": "success",
                "message": "No addons found to populate",
                "addons_processed": 0,
                "addons_skipped": 0,
                "errors": []
            }

        logger.info(f"Discovered {len(all_addons)} addons across all addon paths")

        addons_processed = 0
        addons_skipped = 0
        errors = []
        current_time = datetime.now()

        for addon_name, addon_path in all_addons.items():
            try:


                # Read manifest file
                manifest_path = os.path.join(addon_path, "__manifest__.py")
                manifest_data = read_manifest_file(manifest_path)

                if not manifest_data:
                    logger.warning(
                        f"Skipping addon '{addon_name}': invalid or missing manifest"
                    )
                    addons_skipped += 1
                    continue

                # Extract manifest information with defaults
                display_name = manifest_data.get('name', addon_name)
                summary = manifest_data.get('summary', '')
                description = manifest_data.get('description', display_name)
                author = manifest_data.get('author', '')
                version = manifest_data.get('version', '1.0.0')
                category = manifest_data.get('category', 'Uncategorized')
                website = manifest_data.get('website', '')
                installable = manifest_data.get('installable', True)
                auto_install = manifest_data.get('auto_install', False)
                application = manifest_data.get('application', False)

                # Determine default state - base addon should remain installed
                default_state = 'installed' if addon_name == 'base' else 'uninstalled'

                # Check if addon already exists in the table
                existing_record = await db_manager.fetchrow(
                    "SELECT id, state FROM ir_module_module WHERE name = $1",
                    addon_name
                )

                if existing_record:
                    # Update existing record (but preserve current state)
                    await db_manager.execute(
                        """UPDATE ir_module_module SET
                           display_name = $1, summary = $2, description = $3,
                           author = $4, version = $5, category = $6, website = $7,
                           installable = $8, auto_install = $9, application = $10,
                           update_at = $11
                           WHERE name = $12""",
                        display_name, summary, description, author, version,
                        category, website, installable, auto_install, application,
                        current_time, addon_name
                    )
                    logger.debug(f"Updated existing addon record: {addon_name}")
                else:
                    # Insert new record
                    record_id = str(uuid.uuid4())
                    await db_manager.execute(
                        """INSERT INTO ir_module_module
                           (id, name, display_name, summary, description, author, version,
                            category, website, state, installable, auto_install, application,
                            sort_order, create_at, update_at)
                           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)""",
                        record_id, addon_name, display_name, summary, description,
                        author, version, category, website, default_state,
                        installable, auto_install, application, 100,  # default sort_order
                        current_time, current_time
                    )
                    logger.debug(f"Inserted new addon record: {addon_name} (state: {default_state})")

                addons_processed += 1

            except Exception as e:
                error_msg = f"Failed to process addon '{addon_name}': {e}"
                logger.error(error_msg)
                errors.append(error_msg)
                addons_skipped += 1

        # Log summary
        logger.info(
            f"✅ Completed ir.module.module population: "
            f"{addons_processed} addons processed, {addons_skipped} skipped"
        )

        if errors:
            logger.warning(f"Encountered {len(errors)} errors during population")

        return {
            "status": "success",
            "message": f"Successfully populated {addons_processed} addons",
            "addons_processed": addons_processed,
            "addons_skipped": addons_skipped,
            "total_discovered": len(all_addons),
            "errors": errors
        }

    except Exception as e:
        error_msg = f"Critical error during ir.module.module population: {e}"
        logger.error(error_msg)
        return {
            "status": "error",
            "message": error_msg,
            "addons_processed": 0,
            "addons_skipped": 0,
            "errors": [error_msg]
        }



