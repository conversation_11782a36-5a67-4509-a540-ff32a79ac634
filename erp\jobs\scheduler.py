"""
Job Scheduler - Handles job scheduling and channel assignment
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional

from ..logging import get_logger


class JobScheduler:
    """
    Job scheduler that manages job timing and channel assignment.
    
    This scheduler:
    1. Assigns jobs to appropriate channels
    2. Manages job priorities and scheduling
    3. Handles channel capacity management
    4. Coordinates with job workers
    5. Provides job queue management
    """

    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger(__name__)
        
        # Job tracking
        self.scheduled_jobs: Dict[str, Dict] = {}  # job_uuid -> job_info
        self.channel_queues: Dict[str, List] = {}  # channel_name -> list of jobs
        self.last_database_check: Dict[str, datetime] = {}  # db_name -> last_check_time
        
        # Configuration
        self.check_interval = config.get('job_check_interval', 30)
        self.enable_auto_assignment = config.get('job_auto_channel_assignment', True)
        
        self.logger.info("Job Scheduler initialized")

    async def schedule_jobs_for_database(self, db_name: str) -> List[Dict]:
        """
        Schedule jobs for a specific database.
        
        Args:
            db_name: Database name
            
        Returns:
            List of jobs ready to run
        """
        try:
            # Get all pending jobs from the database
            jobs = await self._get_database_jobs(db_name)
            
            if not jobs:
                return []
            
            # Process each job
            ready_jobs = []
            current_time = datetime.now()
            
            for job in jobs:
                job_uuid = job['uuid']
                
                # Assign channel if not already assigned
                if self.enable_auto_assignment and not job.get('channel_id'):
                    await self._assign_job_to_channel(db_name, job)
                
                # Check if job is ready to run
                if self._is_job_ready(job, current_time):
                    ready_jobs.append(job)
                    self.logger.debug(f"Job {job['name']} ({job_uuid}) is ready to run")
                
                # Store job info
                self.scheduled_jobs[job_uuid] = job
            
            # Update last check time
            self.last_database_check[db_name] = current_time
            
            return ready_jobs
            
        except Exception as e:
            self.logger.error(f"Failed to schedule jobs for database {db_name}: {e}")
            return []

    async def _get_database_jobs(self, db_name: str) -> List[Dict]:
        """Get pending jobs from a database."""
        try:
            from ..environment import EnvironmentManager
            
            # Create environment
            env = await EnvironmentManager.create_environment(db_name, 1)  # Use admin user
            job_model = env['ir.job']
            
            # Get pending jobs
            domain = [
                ('state', '=', 'pending'),
                ('database_name', '=', db_name)
            ]
            
            jobs = job_model.search(domain, order='priority ASC, scheduled_at ASC')
            
            # Convert to dictionaries
            job_data = []
            for job in jobs:
                job_dict = {
                    'id': job.id,
                    'uuid': job.uuid,
                    'name': job.name,
                    'model': job.model,
                    'function': job.function,
                    'args': job.args,
                    'kwargs': job.kwargs,
                    'user_id': job.user_id.id if job.user_id else 1,
                    'database_name': job.database_name,
                    'priority': job.priority,
                    'scheduled_at': job.scheduled_at,
                    'max_retries': job.max_retries,
                    'retry_count': job.retry_count,
                    'logging_enabled': job.logging_enabled,
                    'channel_id': job.channel_id.id if job.channel_id else None,
                }
                job_data.append(job_dict)
            
            return job_data
            
        except Exception as e:
            self.logger.error(f"Failed to get jobs from database {db_name}: {e}")
            return []

    def _is_job_ready(self, job: Dict, current_time: datetime) -> bool:
        """Check if a job is ready to run."""
        scheduled_at = job.get('scheduled_at')
        if not scheduled_at:
            return True
        
        # Handle both datetime objects and strings
        if isinstance(scheduled_at, str):
            try:
                scheduled_at = datetime.fromisoformat(scheduled_at.replace('Z', '+00:00'))
            except ValueError:
                return True  # If we can't parse, assume ready
        
        return scheduled_at <= current_time

    async def _assign_job_to_channel(self, db_name: str, job: Dict):
        """Assign a job to the best available channel."""
        try:
            from ..environment import EnvironmentManager
            
            env = await EnvironmentManager.create_environment(db_name, 1)
            channel_model = env['ir.job.channel']
            
            # Find the best channel for this job
            best_channel = channel_model.get_best_channel_for_job(job)
            
            if best_channel:
                # Update the job with the assigned channel
                job_model = env['ir.job']
                job_record = job_model.browse(job['id'])
                job_record.write({'channel_id': best_channel.id})
                
                # Update our local job data
                job['channel_id'] = best_channel.id
                
                self.logger.debug(f"Assigned job {job['name']} to channel {best_channel.name}")
            else:
                self.logger.warning(f"No suitable channel found for job {job['name']}")
                
        except Exception as e:
            self.logger.error(f"Failed to assign job to channel: {e}")

    def get_job_queue(self, channel_name: str) -> List[Dict]:
        """Get the job queue for a specific channel."""
        return self.channel_queues.get(channel_name, [])

    def clear_job_queue(self, channel_name: str):
        """Clear the job queue for a specific channel."""
        if channel_name in self.channel_queues:
            del self.channel_queues[channel_name]

    def get_scheduled_jobs(self, db_name: str = None) -> Dict:
        """Get scheduled jobs, optionally filtered by database."""
        if db_name:
            # Return jobs for specific database
            return {job_uuid: job for job_uuid, job in self.scheduled_jobs.items() 
                   if job.get('database_name') == db_name}
        
        return dict(self.scheduled_jobs)

    def get_next_jobs(self, channel_name: str = None, limit: int = 10) -> List[Dict]:
        """Get the next jobs to run, optionally filtered by channel."""
        all_jobs = list(self.scheduled_jobs.values())
        
        # Filter by channel if specified
        if channel_name:
            all_jobs = [job for job in all_jobs if job.get('channel_name') == channel_name]
        
        # Filter ready jobs
        current_time = datetime.now()
        ready_jobs = [
            job for job in all_jobs
            if job.get('state') == 'pending' and 
               self._is_job_ready(job, current_time)
        ]
        
        # Sort by priority and scheduled time
        ready_jobs.sort(key=lambda j: (j.get('priority', 5), j.get('scheduled_at', current_time)))
        
        return ready_jobs[:limit]

    async def create_default_channels(self, db_name: str):
        """Create default job channels in a database."""
        try:
            from ..environment import EnvironmentManager
            
            env = await EnvironmentManager.create_environment(db_name, 1)
            channel_model = env['ir.job.channel']
            
            # Create default channels
            channel_model.create_default_channels()
            
            self.logger.info(f"Created default job channels for database {db_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to create default channels for database {db_name}: {e}")

    def get_channel_statistics(self, channel_name: str = None) -> Dict:
        """Get statistics for channels."""
        stats = {}
        
        if channel_name:
            # Stats for specific channel
            channel_jobs = [job for job in self.scheduled_jobs.values() 
                          if job.get('channel_name') == channel_name]
            stats[channel_name] = self._calculate_channel_stats(channel_jobs)
        else:
            # Stats for all channels
            channels = set()
            for job in self.scheduled_jobs.values():
                channel = job.get('channel_name', 'default')
                channels.add(channel)
            
            for channel in channels:
                channel_jobs = [job for job in self.scheduled_jobs.values() 
                              if job.get('channel_name', 'default') == channel]
                stats[channel] = self._calculate_channel_stats(channel_jobs)
        
        return stats

    def _calculate_channel_stats(self, jobs: List[Dict]) -> Dict:
        """Calculate statistics for a list of jobs."""
        total_jobs = len(jobs)
        pending_jobs = len([j for j in jobs if j.get('state') == 'pending'])
        running_jobs = len([j for j in jobs if j.get('state') == 'running'])
        
        return {
            'total_jobs': total_jobs,
            'pending_jobs': pending_jobs,
            'running_jobs': running_jobs,
            'queue_length': pending_jobs
        }

    def cleanup_completed_jobs(self):
        """Remove completed jobs from scheduler memory."""
        completed_states = ['done', 'failed', 'cancelled']
        completed_jobs = [
            job_uuid for job_uuid, job in self.scheduled_jobs.items()
            if job.get('state') in completed_states
        ]
        
        for job_uuid in completed_jobs:
            del self.scheduled_jobs[job_uuid]
        
        if completed_jobs:
            self.logger.debug(f"Cleaned up {len(completed_jobs)} completed jobs from scheduler memory")

    def get_status(self) -> Dict:
        """Get scheduler status."""
        return {
            'scheduled_jobs': len(self.scheduled_jobs),
            'channel_queues': {name: len(queue) for name, queue in self.channel_queues.items()},
            'last_database_check': {db: check_time.isoformat() for db, check_time in self.last_database_check.items()},
            'config': {
                'check_interval': self.check_interval,
                'enable_auto_assignment': self.enable_auto_assignment
            }
        }
