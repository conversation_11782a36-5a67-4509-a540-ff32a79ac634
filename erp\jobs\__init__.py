"""
Job execution system for ERP - Immediate job processing

This package provides a comprehensive job execution system that works alongside
the existing cron system for immediate job processing:

- Job execution in separate processes to avoid blocking the main server
- Multi-database support
- Channel-based job distribution
- Job execution with error handling and retries
- Integration with existing cron worker infrastructure

Main Components:
- JobExecutor: Executes individual jobs
- JobWorker: Worker process that processes jobs from queues
- JobManager: Manages job workers and coordinates with cron system
- JobScheduler: Handles job scheduling and channel assignment
"""

from .executor import JobExecutor
from .manager import JobManager
from .scheduler import JobScheduler
from .worker import JobWorker

__all__ = [
    'JobExecutor',
    'JobManager', 
    'JobScheduler',
    'JobWorker'
]
