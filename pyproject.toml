[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
  | node_modules
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["*/migrations/*", "venv/*", "node_modules/*"]

[tool.flake8]
max-line-length = 88
extend-ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
]
exclude = [
    ".git",
    "__pycache__",
    "venv",
    "node_modules",
    "*.egg-info",
    "build",
    "dist",
]
per-file-ignores = [
    "__init__.py:F401",  # imported but unused
]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

# Exclude patterns
exclude = [
    "venv/",
    "node_modules/",
    "build/",
    "dist/",
    "tests/",
    "examples/",
]

# Per-module options
[[tool.mypy.overrides]]
module = [
    "psycopg2.*",
    "uvicorn.*",
    "fastapi.*",
    "jinja2.*",
    "sqlalchemy.*",
]
ignore_missing_imports = true

[tool.pylint.messages_control]
disable = [
    "C0103",  # invalid-name
    "C0111",  # missing-docstring
    "R0903",  # too-few-public-methods
    "R0913",  # too-many-arguments
    "W0613",  # unused-argument
]

[tool.pylint.format]
max-line-length = 88

[tool.pylint.design]
max-args = 10
max-locals = 20
max-returns = 8
max-branches = 15
max-statements = 60

[tool.coverage.run]
source = ["erp"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/node_modules/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
