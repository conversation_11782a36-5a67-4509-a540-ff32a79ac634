"""
Cron Process Manager - Main manager for the cron system

This module manages the lifecycle of cron worker processes, handles
multi-database support, and coordinates job scheduling across processes.
"""

import asyncio
import multiprocessing
import signal
import time
from typing import Dict, List, Optional, Set
from datetime import datetime

from ..logging import get_logger
from ..process.coordination import get_process_coordinator
from ..config import config


class CronProcessManager:
    """
    Main cron process manager that spawns and manages worker processes.
    
    This manager:
    1. Spawns multiple worker processes based on configuration
    2. Distributes databases across worker processes
    3. Monitors worker process health
    4. Handles graceful shutdown
    5. Manages process restart on failures
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.config = config.cron_config
        
        # Process management
        self.worker_processes: Dict[int, multiprocessing.Process] = {}
        self.database_assignments: Dict[str, int] = {}  # db_name -> process_id
        self.process_databases: Dict[int, Set[str]] = {}  # process_id -> set of db_names
        
        # State management
        self.is_running = False
        self.shutdown_event = multiprocessing.Event()
        self.start_time = None
        
        # Process monitoring
        self.last_health_check = None
        self.failed_processes: Set[int] = set()
        
        self.logger.info("Cron Process Manager initialized")

    async def start(self, databases: List[str] = None):
        """
        Start the cron process manager and spawn worker processes.
        
        Args:
            databases: List of database names to manage. If None, discovers all databases.
        """
        # Use process coordination to prevent duplicate managers
        coordinator = get_process_coordinator()

        if not coordinator.coordinate_startup("cron_manager_start"):
            self.logger.debug("Cron manager startup already in progress, skipping")
            return

        try:
            if self.is_running:
                self.logger.warning("Cron Process Manager is already running")
                return

            self.logger.info("Starting Cron Process Manager")
            self.is_running = True
            self.start_time = datetime.now()
            
            # Discover databases if not provided
            if databases is None:
                databases = await self._discover_databases()
            
            if not databases:
                self.logger.warning("No databases found for cron processing")
                return
            
            self.logger.info(f"Managing cron jobs for {len(databases)} databases: {databases}")
            
            # Distribute databases across processes
            self._distribute_databases(databases)
            
            # Spawn worker processes
            await self._spawn_worker_processes()
            
            # Start monitoring
            await self._start_monitoring()
            
            self.logger.info(f"Cron Process Manager started with {len(self.worker_processes)} worker processes")
            
        except Exception as e:
            self.logger.error("Failed to start Cron Process Manager: %s", e)
            await self.stop()
            raise
        finally:
            coordinator.finish_startup_coordination("cron_manager_start")

    async def stop(self):
        """Stop the cron process manager and all worker processes."""
        if not self.is_running:
            return

        self.logger.info("Stopping Cron Process Manager")
        self.is_running = False
        
        # Signal shutdown to all processes
        self.shutdown_event.set()
        
        # Gracefully terminate worker processes
        await self._terminate_worker_processes()
        
        # Clear state
        self.worker_processes.clear()
        self.database_assignments.clear()
        self.process_databases.clear()
        self.failed_processes.clear()
        
        self.logger.info("Cron Process Manager stopped")

    async def restart_failed_processes(self):
        """Restart any failed worker processes."""
        if not self.is_running:
            return
        
        failed_pids = list(self.failed_processes)
        if not failed_pids:
            return
        
        self.logger.info(f"Restarting {len(failed_pids)} failed processes")
        
        for pid in failed_pids:
            try:
                # Get databases assigned to this process
                databases = self.process_databases.get(pid, set())
                
                # Remove failed process
                if pid in self.worker_processes:
                    del self.worker_processes[pid]
                if pid in self.process_databases:
                    del self.process_databases[pid]
                self.failed_processes.discard(pid)
                
                # Spawn new process for these databases
                if databases:
                    await self._spawn_single_worker(list(databases))
                    
            except Exception as e:
                self.logger.error(f"Failed to restart process {pid}: {e}")

    async def add_database(self, db_name: str):
        """Add a new database to cron processing."""
        if db_name in self.database_assignments:
            self.logger.debug(f"Database {db_name} already assigned to cron processing")
            return
        
        # Find the process with the least databases
        min_count = float('inf')
        target_pid = None
        
        for pid, databases in self.process_databases.items():
            if len(databases) < min_count:
                min_count = len(databases)
                target_pid = pid
        
        if target_pid is None:
            # No processes available, spawn a new one if under limit
            if len(self.worker_processes) < self.config.max_cron_processes:
                await self._spawn_single_worker([db_name])
            else:
                self.logger.warning(f"Cannot add database {db_name}: maximum processes reached")
        else:
            # Assign to existing process
            self.database_assignments[db_name] = target_pid
            self.process_databases[target_pid].add(db_name)
            self.logger.info(f"Assigned database {db_name} to process {target_pid}")

    async def remove_database(self, db_name: str):
        """Remove a database from cron processing."""
        if db_name not in self.database_assignments:
            return
        
        pid = self.database_assignments[db_name]
        
        # Remove from assignments
        del self.database_assignments[db_name]
        self.process_databases[pid].discard(db_name)
        
        # If process has no more databases, consider terminating it
        if not self.process_databases[pid]:
            await self._terminate_single_process(pid)
        
        self.logger.info(f"Removed database {db_name} from cron processing")

    def get_status(self) -> Dict:
        """Get current status of the cron process manager."""
        return {
            "is_running": self.is_running,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "worker_processes": len(self.worker_processes),
            "active_processes": [pid for pid in self.worker_processes.keys() if pid not in self.failed_processes],
            "failed_processes": list(self.failed_processes),
            "managed_databases": len(self.database_assignments),
            "database_assignments": dict(self.database_assignments),
            "config": self.config.config,
        }

    async def _discover_databases(self) -> List[str]:
        """Discover available databases for cron processing."""
        try:
            from ..database.registry import DatabaseRegistry
            
            # Get all registered databases
            databases = list(DatabaseRegistry.get_all_databases().keys())
            self.logger.debug(f"Discovered {len(databases)} databases: {databases}")
            return databases
            
        except Exception as e:
            self.logger.error(f"Failed to discover databases: {e}")
            return []

    def _distribute_databases(self, databases: List[str]):
        """Distribute databases across worker processes."""
        max_processes = min(self.config.max_cron_processes, len(databases))
        
        # Calculate databases per process
        databases_per_process = len(databases) // max_processes
        remainder = len(databases) % max_processes
        
        process_id = 0
        db_index = 0
        
        for i in range(max_processes):
            # Calculate how many databases this process gets
            db_count = databases_per_process + (1 if i < remainder else 0)
            
            # Assign databases to this process
            process_databases = databases[db_index:db_index + db_count]
            
            self.process_databases[process_id] = set(process_databases)
            
            for db_name in process_databases:
                self.database_assignments[db_name] = process_id
            
            db_index += db_count
            process_id += 1
        
        self.logger.info(f"Distributed {len(databases)} databases across {max_processes} processes")

    async def _spawn_worker_processes(self):
        """Spawn all worker processes."""
        spawn_interval = self.config.cron_spawn_interval
        
        for process_id, databases in self.process_databases.items():
            await self._spawn_single_worker(list(databases), process_id)
            
            # Add delay between spawning processes
            if spawn_interval > 0:
                await asyncio.sleep(spawn_interval)

    async def _spawn_single_worker(self, databases: List[str], process_id: int = None) -> int:
        """Spawn a single worker process."""
        if process_id is None:
            process_id = max(self.process_databases.keys(), default=-1) + 1
        
        try:
            from .worker import CronWorker
            
            # Create worker process
            worker = CronWorker(
                process_id=process_id,
                databases=databases,
                config=self.config.config,
                shutdown_event=self.shutdown_event
            )
            
            process = multiprocessing.Process(
                target=worker.run,
                name=f"CronWorker-{process_id}",
                daemon=False
            )
            
            process.start()
            
            # Store process info
            self.worker_processes[process_id] = process
            self.process_databases[process_id] = set(databases)
            
            for db_name in databases:
                self.database_assignments[db_name] = process_id
            
            self.logger.info(f"Spawned worker process {process_id} (PID: {process.pid}) for databases: {databases}")
            return process_id
            
        except Exception as e:
            self.logger.error(f"Failed to spawn worker process {process_id}: {e}")
            raise

    async def _terminate_worker_processes(self):
        """Terminate all worker processes gracefully."""
        if not self.worker_processes:
            return
        
        self.logger.info(f"Terminating {len(self.worker_processes)} worker processes")
        
        # Send SIGTERM to all processes
        for process in self.worker_processes.values():
            if process.is_alive():
                try:
                    process.terminate()
                except Exception as e:
                    self.logger.warning(f"Failed to terminate process {process.pid}: {e}")
        
        # Wait for graceful shutdown
        shutdown_timeout = 30  # seconds
        start_time = time.time()
        
        while time.time() - start_time < shutdown_timeout:
            alive_processes = [p for p in self.worker_processes.values() if p.is_alive()]
            if not alive_processes:
                break
            await asyncio.sleep(1)
        
        # Force kill any remaining processes
        for process in self.worker_processes.values():
            if process.is_alive():
                self.logger.warning(f"Force killing process {process.pid}")
                try:
                    process.kill()
                except Exception as e:
                    self.logger.error(f"Failed to kill process {process.pid}: {e}")
        
        # Join all processes
        for process in self.worker_processes.values():
            try:
                process.join(timeout=5)
            except Exception as e:
                self.logger.warning(f"Failed to join process {process.pid}: {e}")

    async def _terminate_single_process(self, process_id: int):
        """Terminate a single worker process."""
        if process_id not in self.worker_processes:
            return
        
        process = self.worker_processes[process_id]
        
        try:
            if process.is_alive():
                process.terminate()
                process.join(timeout=10)
                
                if process.is_alive():
                    process.kill()
                    process.join(timeout=5)
            
            del self.worker_processes[process_id]
            if process_id in self.process_databases:
                del self.process_databases[process_id]
            
            self.logger.info(f"Terminated worker process {process_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to terminate process {process_id}: {e}")

    async def _start_monitoring(self):
        """Start monitoring worker processes."""
        # This would typically run in a separate task
        # For now, we'll implement basic health checking
        self.last_health_check = datetime.now()

    async def _check_process_health(self):
        """Check health of all worker processes."""
        for process_id, process in list(self.worker_processes.items()):
            if not process.is_alive():
                self.logger.warning(f"Worker process {process_id} (PID: {process.pid}) has died")
                self.failed_processes.add(process_id)


# Global instance
_cron_manager = None


def get_cron_manager() -> CronProcessManager:
    """Get the global cron process manager instance."""
    global _cron_manager
    if _cron_manager is None:
        _cron_manager = CronProcessManager()
    return _cron_manager
