"""
Process Coordination Manager

This module provides coordination between main process and worker processes
to prevent race conditions and duplicate spawning.
"""

import asyncio
import multiprocessing
import threading
from typing import Dict, Set, Optional
from datetime import datetime, timedelta

from ..logging import get_logger


class ProcessCoordinator:
    """
    Coordinates process spawning and lifecycle management to prevent
    duplicate processes and race conditions.
    """
    
    _instance: Optional['ProcessCoordinator'] = None
    _lock = threading.Lock()
    _logger = get_logger(__name__)
    
    def __init__(self):
        self._active_processes: Dict[str, Dict] = {}  # process_type -> process_info
        self._process_lock = threading.Lock()
        self._startup_coordination = {}  # For coordinating startup sequences
        
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def register_process_start(self, process_type: str, process_id: str, 
                             pid: int, databases: list = None) -> bool:
        """
        Register that a process is starting.
        
        Returns:
            True if process was registered successfully
            False if a process of this type is already running
        """
        with self._process_lock:
            if process_type in self._active_processes:
                existing = self._active_processes[process_type]
                # Check if existing process is still alive
                if self._is_process_alive(existing['pid']):
                    self._logger.debug(
                        "Process type %s already running with PID %d, skipping new process",
                        process_type, existing['pid']
                    )
                    return False
                else:
                    self._logger.info(
                        "Existing process %s (PID %d) is dead, allowing new process",
                        process_type, existing['pid']
                    )
            
            # Register new process
            self._active_processes[process_type] = {
                'process_id': process_id,
                'pid': pid,
                'databases': databases or [],
                'start_time': datetime.now(),
                'status': 'running'
            }
            
            self._logger.info(
                "Registered %s process: ID=%s, PID=%d, databases=%s",
                process_type, process_id, pid, databases
            )
            return True
    
    def unregister_process(self, process_type: str):
        """Unregister a process when it stops"""
        with self._process_lock:
            if process_type in self._active_processes:
                process_info = self._active_processes.pop(process_type)
                self._logger.info(
                    "Unregistered %s process: PID=%d",
                    process_type, process_info['pid']
                )
    
    def is_process_type_running(self, process_type: str) -> bool:
        """Check if a process of the given type is currently running"""
        with self._process_lock:
            if process_type not in self._active_processes:
                return False
            
            process_info = self._active_processes[process_type]
            if not self._is_process_alive(process_info['pid']):
                # Clean up dead process
                self._active_processes.pop(process_type)
                self._logger.info(
                    "Cleaned up dead %s process (PID %d)",
                    process_type, process_info['pid']
                )
                return False
            
            return True
    
    def get_active_processes(self) -> Dict[str, Dict]:
        """Get information about all active processes"""
        with self._process_lock:
            # Clean up dead processes first
            dead_processes = []
            for process_type, process_info in self._active_processes.items():
                if not self._is_process_alive(process_info['pid']):
                    dead_processes.append(process_type)
            
            for process_type in dead_processes:
                self._active_processes.pop(process_type)
                self._logger.debug("Cleaned up dead process: %s", process_type)
            
            return self._active_processes.copy()
    
    def _is_process_alive(self, pid: int) -> bool:
        """Check if a process with given PID is still alive"""
        try:
            # On Windows, this will raise ProcessLookupError if process doesn't exist
            # On Unix, it will return True if process exists
            import psutil
            return psutil.pid_exists(pid)
        except ImportError:
            # Fallback method without psutil
            try:
                import os
                import signal
                os.kill(pid, 0)  # Send signal 0 to check if process exists
                return True
            except (OSError, ProcessLookupError):
                return False
    
    def coordinate_startup(self, operation_id: str, timeout_seconds: int = 30) -> bool:
        """
        Coordinate startup operations to prevent race conditions.
        
        Returns:
            True if this process should proceed with the operation
            False if another process is already handling it
        """
        with self._process_lock:
            current_time = datetime.now()
            
            # Clean up old coordination entries
            expired_ops = []
            for op_id, op_info in self._startup_coordination.items():
                if current_time - op_info['start_time'] > timedelta(seconds=timeout_seconds):
                    expired_ops.append(op_id)
            
            for op_id in expired_ops:
                self._startup_coordination.pop(op_id)
                self._logger.debug("Cleaned up expired startup coordination: %s", op_id)
            
            # Check if operation is already in progress
            if operation_id in self._startup_coordination:
                self._logger.debug(
                    "Startup operation %s already in progress, skipping",
                    operation_id
                )
                return False
            
            # Register this operation
            self._startup_coordination[operation_id] = {
                'start_time': current_time,
                'pid': multiprocessing.current_process().pid
            }
            
            self._logger.debug(
                "Coordinating startup operation: %s (PID %d)",
                operation_id, multiprocessing.current_process().pid
            )
            return True
    
    def finish_startup_coordination(self, operation_id: str):
        """Mark a startup operation as completed"""
        with self._process_lock:
            if operation_id in self._startup_coordination:
                self._startup_coordination.pop(operation_id)
                self._logger.debug("Finished startup coordination: %s", operation_id)


# Global coordinator instance
_coordinator: Optional[ProcessCoordinator] = None


def get_process_coordinator() -> ProcessCoordinator:
    """Get the global process coordinator instance"""
    global _coordinator
    if _coordinator is None:
        _coordinator = ProcessCoordinator()
    return _coordinator
