# Job System Documentation

The ERP Job System provides a comprehensive solution for executing background jobs in separate processes, ensuring that long-running tasks don't block the main application server.

## Overview

The job system consists of several key components:

- **IR Job Model** (`ir.job`): Manages individual job records
- **IR Job Channel Model** (`ir.job.channel`): Configures parallel execution channels
- **IR Job Log Model** (`ir.job.log`): Tracks detailed execution logs
- **Job Queue Mixin**: Adds `queue()` method to all models
- **Job Workers**: Separate processes that execute jobs
- **Job Manager**: Coordinates worker processes and channels

## Key Features

- **Separate Process Execution**: Jobs run in isolated worker processes
- **Multi-Database Support**: Workers can handle multiple databases
- **Channel-Based Distribution**: Configure parallel execution channels
- **Priority-Based Scheduling**: Jobs are executed based on priority
- **Automatic Retries**: Failed jobs can be automatically retried
- **Comprehensive Logging**: Detailed execution logs with cleanup
- **Integration with Cron**: Works alongside the existing cron system

## Quick Start

### Basic Job Queuing

```python
# Queue a simple method call
job = await self.queue().my_method(param1, param2)

# Queue with priority and channel
job = await self.queue(
    priority=1,
    channel='high_priority'
).process_important_data()

# Queue with delay
job = await self.queue(delay=300).send_delayed_email()
```

### Advanced Configuration

```python
# Queue with full configuration
job = await self.queue(
    name='Custom Job Name',
    priority=2,
    channel='background',
    max_retries=5,
    logging_enabled=True,
    delay=60
).complex_operation(data={'key': 'value'})
```

### Class Method Queuing

```python
# Queue a class method
job = await MyModel.queue_class_method(
    'my_class_method',
    env,
    priority=1,
    channel='reports'
)
```

## Job Channels

Job channels allow you to configure how jobs are distributed and executed:

### Default Channels

- **default**: General purpose jobs (capacity: 2, priority: 5)
- **high_priority**: Urgent tasks (capacity: 1, priority: 1)
- **background**: Long-running tasks (capacity: 4, priority: 8)
- **email**: Email sending jobs (capacity: 2, priority: 3)
- **reports**: Report generation (capacity: 1, priority: 6)

### Channel Configuration

```python
# Create a custom channel
channel_data = {
    'name': 'custom_channel',
    'description': 'Custom processing channel',
    'capacity': 3,
    'priority': 4,
    'active': True,
    'max_execution_time': 1800,
    'retry_count': 2,
    'retry_delay': 120
}
channel = env['ir.job.channel'].create(channel_data)
```

### Channel Patterns

Channels can automatically match jobs based on patterns:

```python
# Channel that processes specific models
channel.model_pattern = 'sale.*'

# Channel that processes specific functions
channel.function_pattern = 'send_email*'

# Channel that processes specific databases
channel.database_pattern = 'prod_*'
```

## Job States

Jobs progress through the following states:

- **pending**: Job is queued and waiting for execution
- **running**: Job is currently being executed
- **done**: Job completed successfully
- **failed**: Job failed (may be retried)
- **cancelled**: Job was cancelled

## Job Management

### Monitoring Jobs

```python
# Get all pending jobs
pending_jobs = env['ir.job'].search([('state', '=', 'pending')])

# Get jobs for a specific model
model_jobs = await my_model.get_queued_jobs(['pending', 'running'])

# Cancel pending jobs
cancelled_count = await my_model.cancel_queued_jobs('specific_method')
```

### Job Statistics

```python
# Get execution statistics
log_model = env['ir.job.log']
stats = log_model.get_job_statistics(days=30)

print(f"Success rate: {stats['successful_executions'] / stats['total_executions']:.2%}")
print(f"Average duration: {stats['average_duration']:.2f}s")
```

## Configuration

### Job System Configuration

Add to your configuration:

```python
# config.py
class JobConfig:
    enabled = True
    max_job_processes = 2
    job_spawn_interval = 2.0
    job_check_interval = 30
    job_timeout = 3600
    job_max_retries = 3
    job_retry_delay = 300
    job_auto_channel_assignment = True

config.job_config = JobConfig()
```

### Worker Process Configuration

Workers are automatically spawned based on:
- Number of active channels
- Database distribution
- Maximum process limits

## Integration with AppRegistry

The job system is automatically initialized when the AppRegistry is created:

1. Job manager starts alongside cron manager
2. Default channels are created in each database
3. Worker processes are spawned for active channels
4. Jobs are distributed across workers

## Logging and Monitoring

### Execution Logs

Every job execution can be logged with detailed information:

```python
# Enable logging for a job
job = await self.queue(logging_enabled=True).my_method()

# View execution logs
logs = env['ir.job.log'].search([('job_id', '=', job.id)])
for log in logs:
    print(f"State: {log.state}, Duration: {log.duration}s")
```

### Automatic Cleanup

Old logs and completed jobs are automatically cleaned up:

- Job logs older than 7 days are removed daily
- Completed jobs older than 7 days are removed every 6 hours

## Error Handling

### Retry Logic

Failed jobs are automatically retried based on configuration:

```python
# Configure retry behavior
job = await self.queue(
    max_retries=5,
    retry_delay=60  # Wait 60 seconds between retries
).unreliable_operation()
```

### Error Tracking

Errors are tracked in both job records and logs:

```python
# Check job errors
if job.state == 'failed':
    print(f"Last error: {job.last_error}")
    print(f"Retry count: {job.retry_count}")
```

## Performance Considerations

### Channel Capacity

- Set appropriate capacity based on resource availability
- Higher capacity = more parallel jobs but more resource usage
- Monitor system resources when adjusting capacity

### Job Timeouts

- Set realistic timeouts for different job types
- Long-running jobs should use background channels
- Critical jobs should have shorter timeouts

### Database Load

- Jobs create database connections
- Monitor connection pool usage
- Consider database-specific channels for high-load scenarios

## Best Practices

1. **Use Appropriate Channels**: Match job types to suitable channels
2. **Set Realistic Timeouts**: Avoid jobs that run indefinitely
3. **Handle Errors Gracefully**: Design methods to be retry-safe
4. **Monitor Performance**: Track job execution metrics
5. **Clean Up Resources**: Ensure jobs don't leak resources
6. **Use Logging Wisely**: Enable logging for critical jobs only

## Troubleshooting

### Common Issues

1. **Jobs Not Executing**: Check if job workers are running
2. **High Memory Usage**: Reduce channel capacity or job complexity
3. **Database Locks**: Ensure jobs don't hold long transactions
4. **Failed Jobs**: Check error messages and retry configuration

### Debugging

```python
# Check job manager status
from erp.jobs.manager import get_job_manager
manager = get_job_manager()
status = manager.get_status()
print(f"Job manager running: {status['is_running']}")
print(f"Worker processes: {status['worker_processes']}")
```

## API Reference

See the individual model documentation for detailed API information:

- `ir.job`: Job management and execution
- `ir.job.channel`: Channel configuration
- `ir.job.log`: Execution logging
- `JobQueueMixin`: Queue method implementation
