"""
Database manager that coordinates all database operations
"""

from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

import asyncpg

from ..operations.crud import CRUDOperations
from ..operations.schema import SchemaOperations
from .pool import ConnectionPool
from .pool_manager import get_global_pool_manager
from .sql_logger import <PERSON><PERSON><PERSON>og<PERSON>
from .transactions import TransactionManager


class DatabaseManager:
    """Database manager with connection pooling and modular operations"""

    def __init__(self, db_name: Optional[str] = None):
        self.db_name = db_name
        self._shared_connection = None  # For sharing connection with cursor
        self._pool = None  # Will be set by create_pool()

        # Initialize components (pool will be set later)
        self.sql_logger = SQLLogger(self.db_name)
        # Note: transactions, crud, and schema will be initialized after pool creation

    def set_shared_connection(self, connection):
        """Set a shared connection to use instead of pool connections"""
        self._shared_connection = connection

    def clear_shared_connection(self):
        """Clear the shared connection"""
        self._shared_connection = None

    async def create_pool(self):
        """Create connection pool using global pool manager"""
        if self._pool is None:
            global_manager = get_global_pool_manager()
            self._pool = await global_manager.get_pool(self.db_name)

            # Initialize components that depend on the pool
            self.transactions = TransactionManager(self._pool)
            self.crud = CRUDOperations(self._pool, self.sql_logger)
            self.schema = SchemaOperations(self._pool, self.sql_logger)

    async def close_pool(self):
        """Close connection pool via global pool manager"""
        if self._pool is not None:
            global_manager = get_global_pool_manager()
            await global_manager.close_pool(self.db_name)
            self._pool = None

    async def _ensure_pool(self):
        """Ensure pool is initialized"""
        if not self._pool:
            await self.create_pool()

    # Delegate CRUD operations
    async def execute(self, query: str, *args) -> str:
        """Execute a query and return status"""
        if self._shared_connection:
            return await self._shared_connection.execute(query, *args)
        await self._ensure_pool()
        return await self.crud.execute(query, *args)

    async def fetch(self, query: str, *args) -> List[asyncpg.Record]:
        """Fetch multiple rows"""
        if self._shared_connection:
            return await self._shared_connection.fetch(query, *args)
        await self._ensure_pool()
        return await self.crud.fetch(query, *args)

    async def fetchrow(self, query: str, *args) -> Optional[asyncpg.Record]:
        """Fetch single row"""
        if self._shared_connection:
            return await self._shared_connection.fetchrow(query, *args)
        await self._ensure_pool()
        return await self.crud.fetchrow(query, *args)

    async def fetchval(self, query: str, *args) -> Any:
        """Fetch single value"""
        if self._shared_connection:
            return await self._shared_connection.fetchval(query, *args)
        await self._ensure_pool()
        return await self.crud.fetchval(query, *args)

    async def insert(self, table: str, data: Dict[str, Any]) -> Optional[str]:
        """Insert a record and return the ID"""
        await self._ensure_pool()
        return await self.crud.insert(table, data)

    async def update(self, table: str, record_id: str, data: Dict[str, Any]) -> bool:
        """Update a record"""
        await self._ensure_pool()
        return await self.crud.update(table, record_id, data)

    async def delete(self, table: str, record_id: str) -> bool:
        """Delete a record"""
        await self._ensure_pool()
        return await self.crud.delete(table, record_id)

    async def exists(self, table: str, record_id: str) -> bool:
        """Check if a record exists"""
        await self._ensure_pool()
        return await self.crud.exists(table, record_id)

    async def count(
        self, table: str, where_clause: str = "", params: List = None
    ) -> int:
        """Count records in table"""
        await self._ensure_pool()
        return await self.crud.count(table, where_clause, params)

    # Delegate schema operations
    async def create_table(self, table_name: str, columns: Dict[str, str]):
        """Create a table with specified columns"""
        await self._ensure_pool()
        await self.schema.create_table(table_name, columns)

    async def drop_table(self, table_name: str):
        """Drop a table"""
        await self._ensure_pool()
        await self.schema.drop_table(table_name)

    async def table_exists(self, table_name: str) -> bool:
        """Check if a table exists"""
        await self._ensure_pool()
        return await self.schema.table_exists(table_name)

    async def get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """Get column information for a table"""
        await self._ensure_pool()
        return await self.schema.get_table_columns(table_name)

    # Delegate transaction operations
    async def begin_transaction(self):
        """Begin a transaction"""
        await self._ensure_pool()
        return await self.transactions.begin_transaction()

    async def commit_transaction(self):
        """Commit current transaction"""
        await self._ensure_pool()
        return await self.transactions.commit_transaction()

    async def rollback_transaction(self):
        """Rollback current transaction"""
        await self._ensure_pool()
        return await self.transactions.rollback_transaction()

    # Direct access to components for advanced usage
    @property
    def acquire_connection(self):
        """Get connection context manager with auto-initialization"""
        @asynccontextmanager
        async def _auto_init_acquire():
            await self._ensure_pool()
            async with self._pool.acquire_connection() as conn:
                yield conn

        return _auto_init_acquire
