"""
Job Queue Mixin - Adds job queuing capabilities to models
"""
import json
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

from ..logging import get_logger


class JobQueueProxy:
    """Proxy object that captures method calls and queues them as jobs"""
    
    def __init__(self, model_instance, job_config):
        self._model_instance = model_instance
        self._job_config = job_config
        self._logger = get_logger(f"{__name__}.{model_instance._name}")
    
    def __getattr__(self, method_name):
        """Capture method calls and queue them as jobs"""
        if not hasattr(self._model_instance, method_name):
            raise AttributeError(f"'{self._model_instance._name}' object has no attribute '{method_name}'")
        
        def queue_method(*args, **kwargs):
            return self._queue_job(method_name, args, kwargs)
        
        return queue_method
    
    async def _queue_job(self, method_name, args, kwargs):
        """Queue a job for execution"""
        try:
            # Get job model
            job_model = self._model_instance.env['ir.job']
            
            # Prepare job data
            job_data = {
                'name': self._job_config.get('name', f"{self._model_instance._name}.{method_name}"),
                'uuid': str(uuid.uuid4()),
                'model': self._model_instance._name,
                'function': method_name,
                'database_name': self._model_instance.env.cr.db_name,
                'user_id': self._model_instance.env.user.id,
                'priority': self._job_config.get('priority', 5),
                'max_retries': self._job_config.get('max_retries', 3),
                'logging_enabled': self._job_config.get('logging_enabled', True),
            }
            
            # Set scheduled time
            delay = self._job_config.get('delay', 0)
            if delay > 0:
                job_data['scheduled_at'] = datetime.now() + timedelta(seconds=delay)
            else:
                job_data['scheduled_at'] = datetime.now()
            
            # Serialize arguments
            if args:
                job_data['args'] = json.dumps(list(args))
            if kwargs:
                job_data['kwargs'] = json.dumps(kwargs)
            
            # Assign channel if specified
            channel_name = self._job_config.get('channel')
            if channel_name:
                channel_model = self._model_instance.env['ir.job.channel']
                channel = channel_model.search([('name', '=', channel_name)], limit=1)
                if channel:
                    job_data['channel_id'] = channel.id
                else:
                    self._logger.warning(f"Channel '{channel_name}' not found, using default channel assignment")
            
            # Create the job
            job = await job_model.create(job_data)
            
            self._logger.info(f"Queued job {job.uuid}: {job.name}")
            
            return job
            
        except Exception as e:
            self._logger.error(f"Failed to queue job {method_name}: {e}")
            raise


class JobQueueMixin:
    """Mixin that adds job queuing capabilities to models"""
    
    def queue(self, 
              name: Optional[str] = None,
              priority: int = 5,
              delay: int = 0,
              channel: Optional[str] = None,
              max_retries: int = 3,
              logging_enabled: bool = True,
              **kwargs) -> JobQueueProxy:
        """
        Queue method calls for background execution
        
        Args:
            name: Custom name for the job (defaults to model.method)
            priority: Job priority (0=highest, 10=lowest)
            delay: Delay in seconds before job execution
            channel: Channel name for job execution
            max_retries: Maximum number of retry attempts
            logging_enabled: Enable detailed execution logging
            **kwargs: Additional job configuration
        
        Returns:
            JobQueueProxy object that captures method calls
        
        Example:
            # Queue a method call
            job = await self.queue(priority=1, channel='high_priority').process_data(param1, param2)
            
            # Queue with delay
            job = await self.queue(delay=300).send_email(recipient='<EMAIL>')
            
            # Queue with custom configuration
            job = await self.queue(
                name='Custom Job Name',
                priority=2,
                max_retries=5,
                logging_enabled=True
            ).complex_operation(data={'key': 'value'})
        """
        job_config = {
            'name': name,
            'priority': priority,
            'delay': delay,
            'channel': channel,
            'max_retries': max_retries,
            'logging_enabled': logging_enabled,
            **kwargs
        }
        
        return JobQueueProxy(self, job_config)
    
    @classmethod
    async def queue_class_method(cls,
                                 method_name: str,
                                 env,
                                 name: Optional[str] = None,
                                 priority: int = 5,
                                 delay: int = 0,
                                 channel: Optional[str] = None,
                                 max_retries: int = 3,
                                 logging_enabled: bool = True,
                                 *args,
                                 **kwargs) -> 'IrJob':
        """
        Queue a class method for background execution
        
        Args:
            method_name: Name of the class method to call
            env: Environment object
            name: Custom name for the job
            priority: Job priority (0=highest, 10=lowest)
            delay: Delay in seconds before job execution
            channel: Channel name for job execution
            max_retries: Maximum number of retry attempts
            logging_enabled: Enable detailed execution logging
            *args: Arguments to pass to the method
            **kwargs: Keyword arguments to pass to the method
        
        Returns:
            Created job record
        
        Example:
            # Queue a class method
            job = await MyModel.queue_class_method(
                'my_class_method',
                env,
                priority=1,
                channel='background'
            )
        """
        try:
            # Get job model
            job_model = env['ir.job']
            
            # Prepare job data
            job_data = {
                'name': name or f"{cls._name}.{method_name}",
                'uuid': str(uuid.uuid4()),
                'model': cls._name,
                'function': method_name,
                'database_name': env.cr.db_name,
                'user_id': env.user.id,
                'priority': priority,
                'max_retries': max_retries,
                'logging_enabled': logging_enabled,
            }
            
            # Set scheduled time
            if delay > 0:
                job_data['scheduled_at'] = datetime.now() + timedelta(seconds=delay)
            else:
                job_data['scheduled_at'] = datetime.now()
            
            # Serialize arguments
            if args:
                job_data['args'] = json.dumps(list(args))
            if kwargs:
                job_data['kwargs'] = json.dumps(kwargs)
            
            # Assign channel if specified
            if channel:
                channel_model = env['ir.job.channel']
                channel_record = channel_model.search([('name', '=', channel)], limit=1)
                if channel_record:
                    job_data['channel_id'] = channel_record.id
            
            # Create the job
            job = await job_model.create(job_data)
            
            logger = get_logger(f"{__name__}.{cls._name}")
            logger.info(f"Queued class method job {job.uuid}: {job.name}")
            
            return job
            
        except Exception as e:
            logger = get_logger(f"{__name__}.{cls._name}")
            logger.error(f"Failed to queue class method {method_name}: {e}")
            raise
    
    async def get_queued_jobs(self, states=None):
        """
        Get jobs queued for this model instance
        
        Args:
            states: List of job states to filter by (default: all states)
        
        Returns:
            List of job records
        """
        job_model = self.env['ir.job']
        domain = [
            ('model', '=', self._name),
            ('database_name', '=', self.env.cr.db_name)
        ]
        
        if states:
            domain.append(('state', 'in', states))
        
        return job_model.search(domain, order='scheduled_at DESC')
    
    async def cancel_queued_jobs(self, method_name=None):
        """
        Cancel pending jobs for this model instance
        
        Args:
            method_name: Optional method name to filter jobs
        
        Returns:
            Number of jobs cancelled
        """
        domain = [
            ('model', '=', self._name),
            ('database_name', '=', self.env.cr.db_name),
            ('state', '=', 'pending')
        ]
        
        if method_name:
            domain.append(('function', '=', method_name))
        
        job_model = self.env['ir.job']
        pending_jobs = job_model.search(domain)
        
        if pending_jobs:
            pending_jobs.write({'state': 'cancelled'})
            return len(pending_jobs)
        
        return 0
