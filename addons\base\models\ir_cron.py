"""
Cron job model for base addon
"""
import os
from datetime import datetime, timedelta

# Import base model
from erp.models import Model
from erp import fields

# Ensure this addon is available via standardized import
from erp.addons import ensure_addon_import
addon_path = os.path.join(os.path.dirname(__file__), '..')
ensure_addon_import('base', addon_path)


class IrCron(Model):
    """Model for managing scheduled cron jobs"""

    _name = 'ir.cron'
    _description = 'Scheduled Actions'

    # Define SQL constraints for data integrity
    _sql_constraints = [
        ('unique_name', 'UNIQUE (name)', 'Cron job name must be unique'),
        ('check_interval_number_positive',
         'CHECK (interval_number > 0)',
         'Interval number must be positive'),
        ('check_numbercall_positive',
         'CHECK (numbercall >= -1)',
         'Number of calls must be -1 (unlimited) or positive'),
        ('check_priority_range',
         'CHECK (priority >= 0 AND priority <= 10)',
         'Priority must be between 0 and 10'),
    ]

    # Basic job information
    name = fields.Char(
        string='Name', required=True, index=True,
        help='Name of the scheduled action'
    )
    
    active = fields.Boolean(
        string='Active', default=True, required=True, index=True,
        help='Whether this cron job is active'
    )
    
    # Target model and method
    model = fields.Char(
        string='Model', required=True, index=True,
        help='Model on which the method is called'
    )
    
    function = fields.Char(
        string='Function', required=True,
        help='Method to be called on the model'
    )
    
    args = fields.Text(
        string='Arguments',
        help='Arguments to be passed to the method (as Python literal)'
    )
    
    # Scheduling configuration
    interval_number = fields.Integer(
        string='Interval Number', default=1, required=True,
        help='Repeat every x units'
    )
    
    interval_type = fields.Selection([
        ('minutes', 'Minutes'),
        ('hours', 'Hours'),
        ('days', 'Days'),
        ('weeks', 'Weeks'),
        ('months', 'Months'),
    ], string='Interval Unit', default='hours', required=True,
        help='Unit of the interval')
    
    # Cron expression (alternative to interval)
    cron = fields.Char(
        string='Cron Expression',
        help='Cron expression (e.g., "0 */6 * * *" for every 6 hours). '
             'If set, takes precedence over interval settings.'
    )
    
    # Execution control
    numbercall = fields.Integer(
        string='Number of Calls', default=-1, required=True,
        help='Number of times the method is called, -1 for unlimited'
    )
    
    doall = fields.Boolean(
        string='Repeat Missed', default=True, required=True,
        help='Specify if missed occurrences should be executed when the server restarts'
    )
    
    priority = fields.Integer(
        string='Priority', default=5, required=True,
        help='Priority of the job (0=highest, 10=lowest)'
    )
    
    # User context
    user_id = fields.Many2One(
        'res.users', string='User', required=True,
        help='User to use for the cron job execution'
    )
    
    # Execution tracking
    nextcall = fields.Datetime(
        string='Next Execution Date', required=True, index=True,
        help='Next planned execution date for this job'
    )
    
    lastcall = fields.Datetime(
        string='Last Execution Date', readonly=True,
        help='Last execution date for this job'
    )
    
    # Additional metadata
    description = fields.Text(
        string='Description',
        help='Description of what this cron job does'
    )

    # Logging configuration
    logging_enabled = fields.Boolean(
        string='Enable Logging', default=False, required=True,
        help='Enable detailed execution logging for this cron job. '
             'Logs are stored in ir.cron.log and automatically cleaned up after 7 days.'
    )

    def _get_default_nextcall(self):
        """Calculate default next call time"""
        return datetime.now() + timedelta(hours=1)

    def _compute_nextcall(self):
        """Compute the next call time based on interval or cron expression"""
        for record in self:
            if record.cron:
                # Use cron expression
                record.nextcall = record._compute_nextcall_from_cron()
            else:
                # Use interval
                record.nextcall = record._compute_nextcall_from_interval()

    def _compute_nextcall_from_interval(self):
        """Compute next call time from interval settings"""
        if not self.lastcall:
            base_time = datetime.now()
        else:
            base_time = self.lastcall

        if self.interval_type == 'minutes':
            delta = timedelta(minutes=self.interval_number)
        elif self.interval_type == 'hours':
            delta = timedelta(hours=self.interval_number)
        elif self.interval_type == 'days':
            delta = timedelta(days=self.interval_number)
        elif self.interval_type == 'weeks':
            delta = timedelta(weeks=self.interval_number)
        elif self.interval_type == 'months':
            # Approximate months as 30 days
            delta = timedelta(days=self.interval_number * 30)
        else:
            delta = timedelta(hours=1)  # Default fallback

        return base_time + delta

    def _compute_nextcall_from_cron(self):
        """Compute next call time from cron expression"""
        # This will be implemented with croniter library
        # For now, return a default
        return datetime.now() + timedelta(hours=1)

    def _run_job(self):
        """Execute the cron job"""
        try:
            # Get the model
            model_obj = self.env[self.model]
            
            # Parse arguments
            args = []
            kwargs = {}
            if self.args:
                try:
                    import ast
                    parsed_args = ast.literal_eval(self.args)
                    if isinstance(parsed_args, (list, tuple)):
                        args = list(parsed_args)
                    elif isinstance(parsed_args, dict):
                        kwargs = parsed_args
                except (ValueError, SyntaxError):
                    # If parsing fails, pass args as string
                    args = [self.args]
            
            # Get the method
            method = getattr(model_obj, self.function)
            
            # Execute the method
            result = method(*args, **kwargs)
            
            # Update execution tracking
            self.write({
                'lastcall': datetime.now(),
                'numbercall': self.numbercall - 1 if self.numbercall > 0 else self.numbercall
            })
            
            # Compute next call
            self._compute_nextcall()
            
            return result
            
        except Exception as e:
            # Log the error and update lastcall
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Cron job {self.name} failed: {e}")
            
            self.write({'lastcall': datetime.now()})
            raise

    @classmethod
    def get_jobs_to_run(cls, db_name=None):
        """Get cron jobs that are ready to run"""
        domain = [
            ('active', '=', True),
            ('nextcall', '<=', datetime.now()),
        ]
        
        # Filter by numbercall if not unlimited
        domain.append('|')
        domain.append(('numbercall', '=', -1))
        domain.append(('numbercall', '>', 0))
        
        return cls.search(domain)

    def button_execute(self):
        """Manual execution button for testing"""
        return self._run_job()

    async def create_execution_log(self, database_name: str,
                                   worker_process_id: str = None):
        """Create a log entry for this job execution if logging is enabled"""
        if not self.logging_enabled:
            return None

        # Import here to avoid circular imports
        log_model = self.env['ir.cron.log']
        return await log_model.create_log_entry(
            self, database_name, worker_process_id
        )

    @classmethod
    def _system_cleanup(cls):
        """System cleanup cron job"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info("Running system cleanup cron job")

        # Placeholder for actual cleanup logic
        # In a real implementation, this would:
        # - Clean up old log files
        # - Remove temporary files
        # - Clear expired cache entries
        # - etc.

        return "System cleanup completed"

    @classmethod
    def _database_maintenance(cls):
        """Database maintenance cron job"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info("Running database maintenance cron job")

        # Placeholder for actual maintenance logic
        # In a real implementation, this would:
        # - Run VACUUM on database tables
        # - Update table statistics
        # - Reindex tables if needed
        # - etc.

        return "Database maintenance completed"

    @classmethod
    def _test_cron_execution(cls):
        """Test cron job for verification"""
        import logging
        logger = logging.getLogger(__name__)

        current_time = datetime.now()
        logger.info(f"Test cron job executed at {current_time}")

        return f"Test execution at {current_time}"

# The model is automatically registered via the metaclass
