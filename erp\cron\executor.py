"""
Cron Executor - Handles the execution of individual cron jobs

This module provides the core job execution logic, including:
- Job method invocation
- Error handling and retries
- Timeout management
- Result tracking
"""

import asyncio
import time
import traceback
from datetime import datetime
from typing import Any, Dict, Optional, Tuple

from ..logging import get_logger


class CronExecutor:
    """
    Cron job executor that handles the actual execution of cron jobs.
    
    This executor:
    1. Executes individual cron jobs
    2. Handles timeouts and errors
    3. Manages retries
    4. Tracks execution results
    5. Updates job status
    """

    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger(__name__)
        
        # Configuration
        self.job_timeout = config.get('cron_job_timeout', 3600)
        self.max_retries = config.get('cron_max_retries', 3)
        self.retry_delay = config.get('cron_retry_delay', 300)
        
        # Execution tracking
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'timeout_executions': 0,
            'total_execution_time': 0.0,
        }
        
        self.logger.info("Cron Executor initialized")

    async def execute_job(self, db_name: str, job: Dict) -> Tuple[bool, str, Any]:
        """
        Execute a single cron job.
        
        Args:
            db_name: Database name
            job: Job information dictionary
            
        Returns:
            Tuple of (success, error_message, result)
        """
        job_id = job['id']
        job_name = job['name']
        
        start_time = time.time()
        self.execution_stats['total_executions'] += 1
        
        try:
            self.logger.info(f"Executing job {job_name} ({job_id}) in database {db_name}")
            
            # Execute with timeout if configured
            if self.job_timeout > 0:
                result = await asyncio.wait_for(
                    self._execute_job_method(db_name, job),
                    timeout=self.job_timeout
                )
            else:
                result = await self._execute_job_method(db_name, job)
            
            # Update statistics
            execution_time = time.time() - start_time
            self.execution_stats['successful_executions'] += 1
            self.execution_stats['total_execution_time'] += execution_time
            
            self.logger.info(f"Job {job_name} ({job_id}) completed successfully in {execution_time:.2f}s")
            
            return True, "", result
            
        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            self.execution_stats['timeout_executions'] += 1
            self.execution_stats['total_execution_time'] += execution_time
            
            error_msg = f"Job timed out after {self.job_timeout}s"
            self.logger.error(f"Job {job_name} ({job_id}) {error_msg}")
            
            return False, error_msg, None
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.execution_stats['failed_executions'] += 1
            self.execution_stats['total_execution_time'] += execution_time
            
            error_msg = str(e)
            error_trace = traceback.format_exc()
            
            self.logger.error(f"Job {job_name} ({job_id}) failed: {error_msg}")
            self.logger.debug(f"Job {job_name} ({job_id}) traceback:\n{error_trace}")
            
            return False, error_msg, None

    async def _execute_job_method(self, db_name: str, job: Dict) -> Any:
        """Execute the actual job method."""
        try:
            # Create environment with the job's user
            env = await self._create_job_environment(db_name, job)
            
            # Get the target model
            model_name = job['model']
            if model_name not in env:
                raise ValueError(f"Model '{model_name}' not found in database {db_name}")
            
            model_obj = env[model_name]
            
            # Get the method
            function_name = job['function']
            if not hasattr(model_obj, function_name):
                raise ValueError(f"Method '{function_name}' not found on model '{model_name}'")
            
            method = getattr(model_obj, function_name)
            
            # Parse and prepare arguments
            args, kwargs = self._parse_job_arguments(job)
            
            # Execute the method
            if asyncio.iscoroutinefunction(method):
                result = await method(*args, **kwargs)
            else:
                # Run synchronous method in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, lambda: method(*args, **kwargs))
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to execute job method: {e}")
            raise

    async def _create_job_environment(self, db_name: str, job: Dict):
        """Create an environment for job execution."""
        try:
            from ..environment import EnvironmentManager
            
            # Get user ID for the job
            user_id = job.get('user_id', 1)  # Default to admin user
            
            # Create environment
            env = await EnvironmentManager.create_environment(db_name, user_id)
            
            return env
            
        except Exception as e:
            self.logger.error(f"Failed to create environment for job execution: {e}")
            raise

    def _parse_job_arguments(self, job: Dict) -> Tuple[list, dict]:
        """Parse job arguments from the job definition."""
        args = []
        kwargs = {}
        
        args_str = job.get('args', '')
        if args_str:
            try:
                import ast
                parsed_args = ast.literal_eval(args_str)
                
                if isinstance(parsed_args, (list, tuple)):
                    args = list(parsed_args)
                elif isinstance(parsed_args, dict):
                    kwargs = parsed_args
                else:
                    # Single argument
                    args = [parsed_args]
                    
            except (ValueError, SyntaxError) as e:
                self.logger.warning(f"Failed to parse job arguments '{args_str}': {e}")
                # If parsing fails, pass args as string
                args = [args_str]
        
        return args, kwargs

    async def execute_job_with_retries(self, db_name: str, job: Dict) -> Tuple[bool, str, Any]:
        """
        Execute a job with retry logic.
        
        Args:
            db_name: Database name
            job: Job information dictionary
            
        Returns:
            Tuple of (success, error_message, result)
        """
        job_id = job['id']
        job_name = job['name']
        
        last_error = ""
        
        for attempt in range(self.max_retries + 1):
            try:
                success, error_msg, result = await self.execute_job(db_name, job)
                
                if success:
                    if attempt > 0:
                        self.logger.info(f"Job {job_name} ({job_id}) succeeded on attempt {attempt + 1}")
                    return True, "", result
                
                last_error = error_msg
                
                # Don't retry on timeout errors (they're likely to timeout again)
                if "timed out" in error_msg.lower():
                    self.logger.warning(f"Job {job_name} ({job_id}) timed out, not retrying")
                    break
                
                # Don't retry if this is the last attempt
                if attempt < self.max_retries:
                    self.logger.warning(
                        f"Job {job_name} ({job_id}) failed on attempt {attempt + 1}/{self.max_retries + 1}: {error_msg}. "
                        f"Retrying in {self.retry_delay}s"
                    )
                    await asyncio.sleep(self.retry_delay)
                
            except Exception as e:
                last_error = str(e)
                self.logger.error(f"Unexpected error executing job {job_name} ({job_id}) on attempt {attempt + 1}: {e}")
                
                if attempt < self.max_retries:
                    await asyncio.sleep(self.retry_delay)
        
        # All retries failed
        self.logger.error(f"Job {job_name} ({job_id}) failed permanently after {self.max_retries + 1} attempts")
        return False, last_error, None

    async def validate_job(self, db_name: str, job: Dict) -> Tuple[bool, str]:
        """
        Validate that a job can be executed.
        
        Args:
            db_name: Database name
            job: Job information dictionary
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check required fields
            required_fields = ['id', 'name', 'model', 'function']
            for field in required_fields:
                if not job.get(field):
                    return False, f"Missing required field: {field}"
            
            # Create environment to check model and method existence
            env = await self._create_job_environment(db_name, job)
            
            # Check if model exists
            model_name = job['model']
            if model_name not in env:
                return False, f"Model '{model_name}' not found"
            
            model_obj = env[model_name]
            
            # Check if method exists
            function_name = job['function']
            if not hasattr(model_obj, function_name):
                return False, f"Method '{function_name}' not found on model '{model_name}'"
            
            # Validate arguments if present
            if job.get('args'):
                try:
                    self._parse_job_arguments(job)
                except Exception as e:
                    return False, f"Invalid arguments: {e}"
            
            return True, ""
            
        except Exception as e:
            return False, f"Validation error: {e}"

    def get_execution_statistics(self) -> Dict:
        """Get execution statistics."""
        stats = dict(self.execution_stats)
        
        # Calculate derived statistics
        if stats['total_executions'] > 0:
            stats['success_rate'] = stats['successful_executions'] / stats['total_executions']
            stats['failure_rate'] = stats['failed_executions'] / stats['total_executions']
            stats['timeout_rate'] = stats['timeout_executions'] / stats['total_executions']
            stats['average_execution_time'] = stats['total_execution_time'] / stats['total_executions']
        else:
            stats['success_rate'] = 0.0
            stats['failure_rate'] = 0.0
            stats['timeout_rate'] = 0.0
            stats['average_execution_time'] = 0.0
        
        return stats

    def reset_statistics(self):
        """Reset execution statistics."""
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'timeout_executions': 0,
            'total_execution_time': 0.0,
        }
        
        self.logger.info("Execution statistics reset")

    def get_config(self) -> Dict:
        """Get executor configuration."""
        return {
            'job_timeout': self.job_timeout,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
        }
