# Tests Directory

This directory contains all test files for the ERP system using pytest framework.

## Test Structure

The test suite is organized into the following categories:

### Unit Tests
- **`test_fields/`** - Field type validation and functionality
  - `test_base_field.py` - Base field functionality
  - `test_char_field.py` - Character field tests
  - `test_numeric_fields.py` - Integer and Boolean field tests
  - `test_relational_fields.py` - Relational and Selection field tests

- **`test_models/`** - Model behavior and ORM functionality
  - `test_base_model.py` - Base model functionality
  - `test_model_registry.py` - Model registry operations
  - `test_model_hierarchy.py` - Model inheritance and hierarchy

- **`test_database/`** - Database operations and connections
  - `test_manager.py` - Database manager tests
  - `test_registry.py` - Database registry tests
  - `test_memory_registry.py` - Memory registry tests
  - `test_memory_manager.py` - Memory registry manager tests
  - `test_model_metadata_manager.py` - Model metadata management
  - `test_filter_processor.py` - Database filter processing

- **`test_addons/`** - Addon system and management
  - `test_manager.py` - Core addon manager functionality
  - `test_addon_manager.py` - Memory addon manager
  - `test_installer.py` - Addon installation system
  - `test_manifest.py` - Addon manifest handling
  - `test_hooks.py` - Addon hook system
  - `test_core.py` - Core addon functionality
  - `test_exceptions.py` - Addon exception handling
  - `test_lifecycle_manager.py` - Addon lifecycle management

- **`test_http/`** - HTTP routing and controllers
  - `test_routing.py` - Route registration and management
  - `test_controllers.py` - Controller functionality
  - `test_jsonrpc.py` - JSON-RPC protocol handling
  - `test_html_errors.py` - HTML error handling
  - `test_app_registry_route_manager.py` - AppRegistry route manager

- **`test_utils/`** - Utility functions and helpers
  - `test_shared_components.py` - Shared utility components

### Integration Tests
- **`test_integration/`** - End-to-end workflow testing
  - `test_registry_integration.py` - Registry system integration

### Configuration
- **`conftest.py`** - Pytest configuration and fixtures
- **`pytest.ini`** - Pytest settings and markers

## Running Tests

### Run All Tests
```bash
# From project root
pytest
```

### Run Specific Test Categories
```bash
# Unit tests only
pytest -m unit

# Integration tests only
pytest -m integration

# Database tests
pytest -m database

# Addon tests
pytest -m addon
```

### Run Specific Test Files
```bash
# Field tests
pytest tests/test_fields/test_base_field.py
pytest tests/test_fields/test_char_field.py

# Model tests
pytest tests/test_models/test_base_model.py
pytest tests/test_models/test_model_registry.py

# Database tests
pytest tests/test_database/test_manager.py
pytest tests/test_database/test_memory_registry.py

# Addon tests
pytest tests/test_addons/test_manager.py
pytest tests/test_addons/test_installer.py

# HTTP tests
pytest tests/test_http/test_routing.py
pytest tests/test_http/test_controllers.py

# Integration tests
pytest tests/test_integration/test_registry_integration.py
```

### Run Test Categories
```bash
# All field tests
pytest tests/test_fields/

# All model tests
pytest tests/test_models/

# All database tests
pytest tests/test_database/

# All addon tests
pytest tests/test_addons/

# All HTTP tests
pytest tests/test_http/

# All integration tests
pytest tests/test_integration/
```

### Run with Coverage
```bash
pytest --cov=erp --cov-report=html
```

### Run with Verbose Output
```bash
pytest -v
```

## Test Features

- **Async Support** - Full async/await test support
- **Fixtures** - Comprehensive test fixtures for common objects
- **Markers** - Test categorization with pytest markers
- **Cleanup** - Automatic cleanup of test data and registries
- **Mocking** - Mock objects for external dependencies
- **Coverage** - Code coverage reporting

## Writing Tests

### Basic Test Structure
```python
import pytest
from erp.fields import Char, Integer

@pytest.mark.unit
class TestBasicFields:

    def test_char_field_creation(self):
        field = Char(string="Test Field", size=100)
        assert field.string == "Test Field"
        assert field.size == 100

    @pytest.mark.asyncio
    async def test_async_operation(self, memory_registry):
        # Test async operations
        result = await memory_registry.get_routes()
        assert isinstance(result, dict)
```

### Using Fixtures
```python
def test_with_database(test_database):
    # test_database fixture provides clean test database
    assert test_database.startswith('test_')

async def test_with_registry(memory_registry):
    # memory_registry fixture provides clean registry
    routes = await memory_registry.get_routes()
    assert isinstance(routes, dict)
```

## See Also
- `docs/DEVELOPMENT_GUIDE.md` - Development and testing guidelines
- `docs/TECHNICAL_REFERENCE.md` - Technical implementation details
