"""
Job model for immediate job execution
"""
import os
import json
import uuid
from datetime import datetime, timedelta

# Import base model
from erp.models import Model
from erp import fields

# Ensure this addon is available via standardized import
from erp.addons import ensure_addon_import
addon_path = os.path.join(os.path.dirname(__file__), '..')
ensure_addon_import('base', addon_path)


class IrJob(Model):
    """Model for managing immediate job execution"""

    _name = 'ir.job'
    _description = 'Job Queue'

    # Define SQL constraints for data integrity
    _sql_constraints = [
        ('check_priority_range',
         'CHECK (priority >= 0 AND priority <= 10)',
         'Priority must be between 0 and 10'),
        ('check_retry_count_positive',
         'CHECK (retry_count >= 0)',
         'Retry count must be non-negative'),
        ('check_max_retries_positive',
         'CHECK (max_retries >= 0)',
         'Max retries must be non-negative'),
    ]

    # Basic job information
    name = fields.Char(
        string='Job Name', required=True, index=True,
        help='Descriptive name for this job'
    )
    
    uuid = fields.Char(
        string='Job UUID', required=True, unique=True, index=True,
        default=lambda: str(uuid.uuid4()),
        help='Unique identifier for this job'
    )
    
    state = fields.Selection([
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('done', 'Done'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ], string='State', default='pending', required=True, index=True,
        help='Current state of the job')
    
    # Job execution details
    model = fields.Char(
        string='Model', required=True, index=True,
        help='Model on which the method will be called'
    )
    
    function = fields.Char(
        string='Function', required=True,
        help='Method to be called on the model'
    )
    
    args = fields.Text(
        string='Arguments',
        help='Arguments to be passed to the method (as JSON)'
    )
    
    kwargs = fields.Text(
        string='Keyword Arguments',
        help='Keyword arguments to be passed to the method (as JSON)'
    )
    
    # Execution context
    user_id = fields.Many2One(
        'res.users', string='User', required=True,
        help='User context for job execution'
    )
    
    database_name = fields.Char(
        string='Database', required=True, index=True,
        help='Database in which to execute the job'
    )
    
    # Scheduling and priority
    priority = fields.Integer(
        string='Priority', default=5, required=True, index=True,
        help='Job priority (0=highest, 10=lowest)'
    )
    
    scheduled_at = fields.Datetime(
        string='Scheduled At', default=lambda: datetime.now(), required=True, index=True,
        help='When this job should be executed'
    )
    
    # Channel assignment
    channel_id = fields.Many2One(
        'ir.job.channel', string='Channel', index=True,
        help='Channel assigned to execute this job'
    )
    
    # Execution tracking
    started_at = fields.Datetime(
        string='Started At', readonly=True,
        help='When job execution started'
    )
    
    finished_at = fields.Datetime(
        string='Finished At', readonly=True,
        help='When job execution finished'
    )
    
    duration = fields.Float(
        string='Duration (seconds)', readonly=True,
        help='Job execution duration in seconds'
    )
    
    # Error handling and retries
    retry_count = fields.Integer(
        string='Retry Count', default=0, readonly=True,
        help='Number of times this job has been retried'
    )
    
    max_retries = fields.Integer(
        string='Max Retries', default=3,
        help='Maximum number of retry attempts'
    )
    
    last_error = fields.Text(
        string='Last Error', readonly=True,
        help='Error message from the last failed execution'
    )
    
    # Result and logging
    result = fields.Text(
        string='Result', readonly=True,
        help='Result returned by the job method (as JSON)'
    )
    
    logging_enabled = fields.Boolean(
        string='Enable Logging', default=True, required=True,
        help='Enable detailed execution logging for this job'
    )
    
    # Worker process tracking
    worker_pid = fields.Integer(
        string='Worker Process ID', readonly=True,
        help='Process ID of the worker executing this job'
    )

    def get_parsed_args(self):
        """Parse and return job arguments"""
        if not self.args:
            return []
        
        try:
            return json.loads(self.args)
        except (json.JSONDecodeError, TypeError):
            return []

    def get_parsed_kwargs(self):
        """Parse and return job keyword arguments"""
        if not self.kwargs:
            return {}
        
        try:
            return json.loads(self.kwargs)
        except (json.JSONDecodeError, TypeError):
            return {}

    def set_args(self, args):
        """Set job arguments as JSON"""
        self.args = json.dumps(args) if args else None

    def set_kwargs(self, kwargs):
        """Set job keyword arguments as JSON"""
        self.kwargs = json.dumps(kwargs) if kwargs else None

    def set_result(self, result):
        """Set job result as JSON"""
        try:
            self.result = json.dumps(result) if result is not None else None
        except (TypeError, ValueError):
            # If result is not JSON serializable, convert to string
            self.result = str(result)

    def mark_running(self, worker_pid=None):
        """Mark job as running"""
        self.write({
            'state': 'running',
            'started_at': datetime.now(),
            'worker_pid': worker_pid
        })

    def mark_done(self, result=None):
        """Mark job as completed successfully"""
        now = datetime.now()
        duration = (now - self.started_at).total_seconds() if self.started_at else 0
        
        update_data = {
            'state': 'done',
            'finished_at': now,
            'duration': duration
        }
        
        if result is not None:
            update_data['result'] = json.dumps(result) if result is not None else None
        
        self.write(update_data)

    def mark_failed(self, error_message=None):
        """Mark job as failed"""
        now = datetime.now()
        duration = (now - self.started_at).total_seconds() if self.started_at else 0
        
        update_data = {
            'state': 'failed',
            'finished_at': now,
            'duration': duration,
            'retry_count': self.retry_count + 1
        }
        
        if error_message:
            update_data['last_error'] = str(error_message)
        
        self.write(update_data)

    def can_retry(self):
        """Check if job can be retried"""
        return (self.state == 'failed' and 
                self.retry_count < self.max_retries)

    def schedule_retry(self, delay_seconds=300):
        """Schedule job for retry"""
        if not self.can_retry():
            return False
        
        retry_time = datetime.now() + timedelta(seconds=delay_seconds)
        self.write({
            'state': 'pending',
            'scheduled_at': retry_time,
            'started_at': None,
            'finished_at': None,
            'worker_pid': None
        })
        return True

    @classmethod
    def get_pending_jobs(cls, channel_id=None, limit=None):
        """Get pending jobs ready for execution"""
        domain = [
            ('state', '=', 'pending'),
            ('scheduled_at', '<=', datetime.now())
        ]
        
        if channel_id:
            domain.append(('channel_id', '=', channel_id))
        
        order = 'priority ASC, scheduled_at ASC'
        return cls.search(domain, order=order, limit=limit)

    @classmethod
    def cleanup_old_jobs(cls, days=7):
        """Clean up old completed/failed jobs"""
        cutoff_date = datetime.now() - timedelta(days=days)
        old_jobs = cls.search([
            ('state', 'in', ['done', 'failed', 'cancelled']),
            ('finished_at', '<', cutoff_date)
        ])
        
        if old_jobs:
            old_jobs.unlink()
            return len(old_jobs)
        return 0

    async def create_execution_log(self, worker_process_id=None):
        """Create a log entry for this job execution if logging is enabled"""
        if not self.logging_enabled:
            return None

        # Import here to avoid circular imports
        log_model = self.env['ir.job.log']
        return await log_model.create_log_entry(
            self, self.database_name, worker_process_id
        )

# The model is automatically registered via the metaclass
