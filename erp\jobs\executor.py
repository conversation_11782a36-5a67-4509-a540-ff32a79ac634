"""
Job Executor - Handles the actual execution of individual jobs
"""

import asyncio
import json
import time
import traceback
from datetime import datetime
from typing import Dict, Optional, Tuple

from ..logging import get_logger


class JobExecutor:
    """
    Job executor that handles the actual execution of individual jobs.
    
    This executor:
    1. Executes individual jobs
    2. Handles timeouts and errors
    3. Manages retries
    4. Tracks execution results
    5. Updates job status and logs
    """

    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger(__name__)
        
        # Configuration
        self.job_timeout = config.get('job_timeout', 3600)
        self.max_retries = config.get('job_max_retries', 3)
        self.retry_delay = config.get('job_retry_delay', 300)
        
        # Execution tracking
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'timeout_executions': 0,
            'total_execution_time': 0.0,
        }
        
        self.logger.info("Job Executor initialized")

    async def execute_job(self, db_name: str, job: Dict) -> Tuple[bool, str, any]:
        """
        Execute a single job.
        
        Args:
            db_name: Database name
            job: Job data dictionary
            
        Returns:
            Tuple of (success, error_message, result)
        """
        job_name = job.get('name', 'Unknown Job')
        job_id = job.get('id', 'Unknown ID')
        start_time = time.time()
        
        self.execution_stats['total_executions'] += 1
        
        try:
            self.logger.info(f"Executing job {job_name} ({job_id}) in database {db_name}")
            
            # Execute with timeout if configured
            if self.job_timeout > 0:
                result = await asyncio.wait_for(
                    self._execute_job_method(db_name, job),
                    timeout=self.job_timeout
                )
            else:
                result = await self._execute_job_method(db_name, job)
            
            # Update statistics
            execution_time = time.time() - start_time
            self.execution_stats['successful_executions'] += 1
            self.execution_stats['total_execution_time'] += execution_time
            
            self.logger.info(f"Job {job_name} ({job_id}) completed successfully in {execution_time:.2f}s")
            
            return True, "", result
            
        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            self.execution_stats['timeout_executions'] += 1
            self.execution_stats['total_execution_time'] += execution_time
            
            error_msg = f"Job {job_name} ({job_id}) timed out after {self.job_timeout}s"
            self.logger.error(error_msg)
            
            return False, error_msg, None
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.execution_stats['failed_executions'] += 1
            self.execution_stats['total_execution_time'] += execution_time
            
            error_msg = f"Job {job_name} ({job_id}) failed: {str(e)}"
            self.logger.error(error_msg)
            self.logger.debug(f"Job {job_name} traceback: {traceback.format_exc()}")
            
            return False, error_msg, None

    async def _execute_job_method(self, db_name: str, job: Dict):
        """Execute the actual job method."""
        try:
            from ..environment import EnvironmentManager
            
            # Create environment with the job's user
            user_id = job.get('user_id', 1)
            env = await EnvironmentManager.create_environment(db_name, user_id)
            
            # Get the target model
            model_name = job['model']
            model_obj = env[model_name]
            
            # Get the method
            function_name = job['function']
            method = getattr(model_obj, function_name)
            
            # Parse and prepare arguments
            args, kwargs = self._parse_job_arguments(job)
            
            # Execute the method
            if asyncio.iscoroutinefunction(method):
                result = await method(*args, **kwargs)
            else:
                # Run synchronous method in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, lambda: method(*args, **kwargs))
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to execute job method: {e}")
            raise

    def _parse_job_arguments(self, job: Dict) -> Tuple[list, dict]:
        """Parse job arguments from JSON."""
        args = []
        kwargs = {}
        
        # Parse positional arguments
        if job.get('args'):
            try:
                args = json.loads(job['args'])
                if not isinstance(args, list):
                    args = [args]  # Convert single value to list
            except (json.JSONDecodeError, TypeError):
                # If parsing fails, treat as string argument
                args = [job['args']]
        
        # Parse keyword arguments
        if job.get('kwargs'):
            try:
                kwargs = json.loads(job['kwargs'])
                if not isinstance(kwargs, dict):
                    kwargs = {}  # Invalid kwargs, ignore
            except (json.JSONDecodeError, TypeError):
                kwargs = {}
        
        return args, kwargs

    async def execute_job_with_retry(self, db_name: str, job: Dict) -> Tuple[bool, str, any]:
        """
        Execute a job with retry logic.
        
        Args:
            db_name: Database name
            job: Job data dictionary
            
        Returns:
            Tuple of (success, error_message, result)
        """
        job_name = job.get('name', 'Unknown Job')
        job_id = job.get('id', 'Unknown ID')
        max_retries = job.get('max_retries', self.max_retries)
        
        last_error = ""
        
        for attempt in range(max_retries + 1):
            try:
                success, error_msg, result = await self.execute_job(db_name, job)
                
                if success:
                    if attempt > 0:
                        self.logger.info(f"Job {job_name} ({job_id}) succeeded on attempt {attempt + 1}")
                    return True, "", result
                
                last_error = error_msg
                
                # Don't retry on timeout errors (they're likely to timeout again)
                if "timed out" in error_msg.lower():
                    self.logger.warning(f"Job {job_name} ({job_id}) timed out, not retrying")
                    break
                
                # Don't retry if this is the last attempt
                if attempt < max_retries:
                    retry_delay = job.get('retry_delay', self.retry_delay)
                    self.logger.warning(
                        f"Job {job_name} ({job_id}) failed on attempt {attempt + 1}/{max_retries + 1}: {error_msg}. "
                        f"Retrying in {retry_delay}s"
                    )
                    await asyncio.sleep(retry_delay)
                
            except Exception as e:
                last_error = str(e)
                self.logger.error(f"Unexpected error during job execution: {e}")
                break
        
        self.logger.error(f"Job {job_name} ({job_id}) failed after {max_retries + 1} attempts: {last_error}")
        return False, last_error, None

    def get_execution_stats(self) -> Dict:
        """Get execution statistics."""
        stats = dict(self.execution_stats)
        
        # Calculate success rate
        if stats['total_executions'] > 0:
            stats['success_rate'] = stats['successful_executions'] / stats['total_executions']
            stats['average_execution_time'] = stats['total_execution_time'] / stats['total_executions']
        else:
            stats['success_rate'] = 0.0
            stats['average_execution_time'] = 0.0
        
        return stats

    def reset_stats(self):
        """Reset execution statistics."""
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'timeout_executions': 0,
            'total_execution_time': 0.0,
        }
        self.logger.info("Execution statistics reset")

    async def get_performance_metrics(self) -> Dict:
        """Get current performance metrics for the executor."""
        import psutil
        import os

        try:
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()

            return {
                'memory_usage': memory_info.rss / 1024 / 1024,  # MB
                'cpu_percent': process.cpu_percent(),
                'num_threads': process.num_threads(),
                'execution_stats': self.get_execution_stats()
            }
        except Exception as e:
            self.logger.warning(f"Failed to get performance metrics: {e}")
            return {'execution_stats': self.get_execution_stats()}
