"""
Cron Scheduler - Handles job scheduling and timing calculations

This module provides the scheduling logic for cron jobs, including:
- Reading jobs from the database
- Calculating next execution times
- Managing job queues
- Coordinating with worker processes
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set

from ..logging import get_logger
from .utils import CronUtils


class CronScheduler:
    """
    Cron scheduler that manages job timing and execution coordination.
    
    This scheduler:
    1. Reads cron jobs from databases
    2. Calculates next execution times
    3. Maintains job queues
    4. Coordinates with worker processes
    5. Handles missed jobs and retries
    """

    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger(__name__)
        
        # Job tracking
        self.scheduled_jobs: Dict[str, Dict] = {}  # job_id -> job_info
        self.job_queues: Dict[str, List] = {}  # db_name -> list of jobs
        self.last_database_check: Dict[str, datetime] = {}  # db_name -> last_check_time
        
        # Configuration
        self.check_interval = config.get('cron_check_interval', 60)
        self.enable_missed_jobs = config.get('cron_enable_missed_jobs', True)
        
        self.logger.info("Cron Scheduler initialized")

    async def schedule_jobs_for_database(self, db_name: str) -> List[Dict]:
        """
        Schedule jobs for a specific database.
        
        Args:
            db_name: Database name
            
        Returns:
            List of jobs ready to run
        """
        try:
            # Get all active cron jobs from the database
            jobs = await self._get_database_jobs(db_name)
            
            if not jobs:
                return []
            
            # Process each job
            ready_jobs = []
            current_time = datetime.now()
            
            for job in jobs:
                job_id = job['id']
                
                # Update job scheduling info
                self._update_job_schedule(job, current_time)
                
                # Check if job is ready to run
                if self._is_job_ready(job, current_time):
                    ready_jobs.append(job)
                    self.logger.debug(f"Job {job['name']} ({job_id}) is ready to run")
                
                # Store job info
                self.scheduled_jobs[job_id] = job
            
            # Update last check time
            self.last_database_check[db_name] = current_time
            
            # Update job queue for this database
            self.job_queues[db_name] = ready_jobs
            
            if ready_jobs:
                self.logger.info(f"Scheduled {len(ready_jobs)} jobs for database {db_name}")
            
            return ready_jobs
            
        except Exception as e:
            self.logger.error(f"Failed to schedule jobs for database {db_name}: {e}")
            return []

    async def _get_database_jobs(self, db_name: str) -> List[Dict]:
        """Get all active cron jobs from a database."""
        try:
            from ..environment import EnvironmentManager
            
            # Create environment for this database
            env = await EnvironmentManager.create_environment(db_name, 1)  # Use admin user
            
            # Get the ir.cron model
            cron_model = env['ir.cron']
            
            # Search for active jobs
            domain = [('active', '=', True)]
            jobs = await cron_model.search(domain)
            
            # Convert to list of dictionaries
            job_list = []
            for job in jobs:
                job_data = {
                    'id': job.id,
                    'name': job.name,
                    'model': job.model,
                    'function': job.function,
                    'args': job.args,
                    'interval_number': job.interval_number,
                    'interval_type': job.interval_type,
                    'cron': job.cron,
                    'numbercall': job.numbercall,
                    'doall': job.doall,
                    'priority': job.priority,
                    'user_id': job.user_id,
                    'nextcall': job.nextcall,
                    'lastcall': job.lastcall,
                    'active': job.active,
                }
                job_list.append(job_data)
            
            return job_list
            
        except Exception as e:
            self.logger.error(f"Failed to get jobs from database {db_name}: {e}")
            return []

    def _update_job_schedule(self, job: Dict, current_time: datetime):
        """Update job scheduling information."""
        job_id = job['id']
        
        # Calculate next execution time if not set or if it's in the past
        if not job.get('nextcall') or job['nextcall'] <= current_time:
            next_call = self._calculate_next_execution(job, current_time)
            job['nextcall'] = next_call
            
            self.logger.debug(f"Updated next call for job {job['name']} ({job_id}) to {next_call}")

    def _calculate_next_execution(self, job: Dict, from_time: datetime) -> datetime:
        """Calculate the next execution time for a job."""
        # Use cron expression if available
        if job.get('cron'):
            next_time = CronUtils.calculate_next_run(job['cron'], from_time)
            if next_time:
                return next_time
        
        # Fall back to interval-based scheduling
        return CronUtils.calculate_interval_next_run(
            job['interval_number'],
            job['interval_type'],
            job.get('lastcall') or from_time
        )

    def _is_job_ready(self, job: Dict, current_time: datetime) -> bool:
        """Check if a job is ready to run."""
        # Check if job is active
        if not job.get('active', True):
            return False
        
        # Check if job has remaining calls (if limited)
        if job.get('numbercall', -1) == 0:
            return False
        
        # Check if it's time to run
        next_call = job.get('nextcall')
        if not next_call:
            return False
        
        # Job is ready if next call time has passed
        is_ready = next_call <= current_time
        
        # Handle missed jobs
        if is_ready and not self.enable_missed_jobs:
            # If missed jobs are disabled, only run if within the check interval
            time_diff = (current_time - next_call).total_seconds()
            if time_diff > self.check_interval:
                self.logger.debug(f"Skipping missed job {job['name']} (missed by {time_diff}s)")
                return False
        
        return is_ready

    async def update_job_after_execution(self, db_name: str, job_id: str, success: bool):
        """Update job information after execution."""
        try:
            from ..environment import EnvironmentManager
            
            # Create environment for this database
            env = await EnvironmentManager.create_environment(db_name, 1)
            
            # Get the job record
            cron_model = env['ir.cron']
            job = await cron_model.browse([job_id])
            
            if not job:
                self.logger.warning(f"Job {job_id} not found in database {db_name}")
                return
            
            current_time = datetime.now()
            
            # Update last call time
            update_values = {'lastcall': current_time}
            
            # Update number of calls if limited
            if job.numbercall > 0:
                update_values['numbercall'] = job.numbercall - 1
            
            # Calculate next call time
            if success and (job.numbercall == -1 or job.numbercall > 1):
                # Job will run again, calculate next time
                if job.cron:
                    next_call = CronUtils.calculate_next_run(job.cron, current_time)
                else:
                    next_call = CronUtils.calculate_interval_next_run(
                        job.interval_number,
                        job.interval_type,
                        current_time
                    )
                
                if next_call:
                    update_values['nextcall'] = next_call
            
            # Update the job record
            await job.write(update_values)
            
            # Update local cache
            if job_id in self.scheduled_jobs:
                self.scheduled_jobs[job_id].update(update_values)
            
            self.logger.debug(f"Updated job {job.name} ({job_id}) after execution")
            
        except Exception as e:
            self.logger.error(f"Failed to update job {job_id} after execution: {e}")

    def get_job_queue(self, db_name: str) -> List[Dict]:
        """Get the job queue for a database."""
        return self.job_queues.get(db_name, [])

    def clear_job_queue(self, db_name: str):
        """Clear the job queue for a database."""
        if db_name in self.job_queues:
            del self.job_queues[db_name]

    def get_scheduled_jobs(self, db_name: str = None) -> Dict:
        """Get scheduled jobs, optionally filtered by database."""
        if db_name:
            # Return jobs for specific database
            return {job_id: job for job_id, job in self.scheduled_jobs.items() 
                   if job.get('db_name') == db_name}
        
        return dict(self.scheduled_jobs)

    def get_next_jobs(self, limit: int = 10) -> List[Dict]:
        """Get the next jobs to run across all databases."""
        all_jobs = list(self.scheduled_jobs.values())
        
        # Filter active jobs with valid next call times
        ready_jobs = [
            job for job in all_jobs
            if job.get('active', True) and 
               job.get('nextcall') and
               job.get('numbercall', -1) != 0
        ]
        
        # Sort by next call time and priority
        ready_jobs.sort(key=lambda j: (j['nextcall'], j.get('priority', 5)))
        
        return ready_jobs[:limit]

    def get_overdue_jobs(self, threshold_minutes: int = 5) -> List[Dict]:
        """Get jobs that are overdue by more than the threshold."""
        current_time = datetime.now()
        threshold = timedelta(minutes=threshold_minutes)
        
        overdue_jobs = []
        
        for job in self.scheduled_jobs.values():
            if (job.get('active', True) and 
                job.get('nextcall') and
                job.get('numbercall', -1) != 0 and
                current_time - job['nextcall'] > threshold):
                
                overdue_jobs.append(job)
        
        return overdue_jobs

    def get_statistics(self) -> Dict:
        """Get scheduler statistics."""
        current_time = datetime.now()
        
        total_jobs = len(self.scheduled_jobs)
        active_jobs = sum(1 for job in self.scheduled_jobs.values() if job.get('active', True))
        ready_jobs = sum(1 for job in self.scheduled_jobs.values() 
                        if job.get('active', True) and 
                           job.get('nextcall') and 
                           job['nextcall'] <= current_time)
        
        overdue_jobs = len(self.get_overdue_jobs())
        
        return {
            'total_jobs': total_jobs,
            'active_jobs': active_jobs,
            'ready_jobs': ready_jobs,
            'overdue_jobs': overdue_jobs,
            'managed_databases': len(self.job_queues),
            'last_check_times': {db: time.isoformat() for db, time in self.last_database_check.items()},
            'config': {
                'check_interval': self.check_interval,
                'enable_missed_jobs': self.enable_missed_jobs,
            }
        }
