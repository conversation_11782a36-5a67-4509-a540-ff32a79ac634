<?xml version="1.0" encoding="utf-8"?>
<erp>
    <data>
        <!-- System Cleanup Cron Job -->
        <record id="cron_job_system_cleanup" model="ir.cron">
            <field name="name">System Cleanup</field>
            <field name="model">ir.cron</field>
            <field name="function">_system_cleanup</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall">True</field>
            <field name="active">True</field>
            <field name="priority">7</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="description">Daily system cleanup including old logs and temporary files</field>
            <field name="logging_enabled">True</field>
        </record>

        <!-- Database Maintenance Cron Job -->
        <record id="cron_job_database_maintenance" model="ir.cron">
            <field name="name">Database Maintenance</field>
            <field name="model">ir.cron</field>
            <field name="function">_database_maintenance</field>
            <field name="interval_number">1</field>
            <field name="interval_type">weeks</field>
            <field name="numbercall">-1</field>
            <field name="doall">True</field>
            <field name="active">True</field>
            <field name="priority">8</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="description">Weekly database maintenance including VACUUM and statistics updates</field>
            <field name="logging_enabled">True</field>
        </record>

        <!-- Job Log Cleanup Cron Job -->
        <record id="cron_job_log_cleanup" model="ir.cron">
            <field name="name">Job Log Cleanup</field>
            <field name="model">ir.job.log</field>
            <field name="function">_system_cleanup_job_logs</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall">True</field>
            <field name="active">True</field>
            <field name="priority">6</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="description">Daily cleanup of old job execution logs (older than 7 days)</field>
            <field name="logging_enabled">False</field>
        </record>

        <!-- Old Job Cleanup Cron Job -->
        <record id="cron_job_cleanup_old_jobs" model="ir.cron">
            <field name="name">Old Job Cleanup</field>
            <field name="model">ir.job</field>
            <field name="function">cleanup_old_jobs</field>
            <field name="interval_number">6</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="doall">True</field>
            <field name="active">True</field>
            <field name="priority">6</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="description">Cleanup completed and failed jobs older than 7 days</field>
            <field name="logging_enabled">False</field>
        </record>

        <!-- Test Cron Job (disabled by default) -->
        <record id="cron_job_test" model="ir.cron">
            <field name="name">Test Cron Execution</field>
            <field name="model">ir.cron</field>
            <field name="function">_test_cron_execution</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">5</field>
            <field name="doall">False</field>
            <field name="active">False</field>
            <field name="priority">9</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="description">Test cron job for verification (disabled by default)</field>
            <field name="logging_enabled">True</field>
        </record>
    </data>
</erp>
