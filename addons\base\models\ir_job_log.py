"""
Job execution logging model
"""
import os
import json
from datetime import datetime, timedelta

# Import base model
from erp.models import Model
from erp import fields

# Ensure this addon is available via standardized import
from erp.addons import ensure_addon_import
addon_path = os.path.join(os.path.dirname(__file__), '..')
ensure_addon_import('base', addon_path)


class IrJobLog(Model):
    """Model for logging job execution details"""

    _name = 'ir.job.log'
    _description = 'Job Execution Logs'

    # Basic log information
    job_id = fields.Many2One(
        'ir.job', string='Job', required=True, index=True, ondelete='cascade',
        help='Job that this log entry belongs to'
    )
    
    job_uuid = fields.Char(
        string='Job UUID', required=True, index=True,
        help='UUID of the job for quick lookup'
    )
    
    job_name = fields.Char(
        string='Job Name', required=True, index=True,
        help='Name of the job at execution time'
    )
    
    # Execution context
    database_name = fields.Char(
        string='Database', required=True, index=True,
        help='Database where the job was executed'
    )
    
    user_id = fields.Many2One(
        'res.users', string='User', required=True,
        help='User context for job execution'
    )
    
    model = fields.Char(
        string='Model', required=True, index=True,
        help='Model on which the method was called'
    )
    
    function = fields.Char(
        string='Function', required=True,
        help='Method that was called'
    )
    
    # Execution details
    state = fields.Selection([
        ('started', 'Started'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('timeout', 'Timeout'),
        ('cancelled', 'Cancelled'),
    ], string='State', required=True, index=True,
        help='Final state of the job execution')
    
    started_at = fields.Datetime(
        string='Started At', required=True, index=True,
        help='When job execution started'
    )
    
    finished_at = fields.Datetime(
        string='Finished At', index=True,
        help='When job execution finished'
    )
    
    duration = fields.Float(
        string='Duration (seconds)',
        help='Job execution duration in seconds'
    )
    
    # Worker information
    worker_process_id = fields.Char(
        string='Worker Process ID',
        help='ID of the worker process that executed the job'
    )
    
    worker_pid = fields.Integer(
        string='Worker PID',
        help='System process ID of the worker'
    )
    
    channel_id = fields.Many2One(
        'ir.job.channel', string='Channel',
        help='Channel that executed the job'
    )
    
    # Execution results
    result = fields.Text(
        string='Result',
        help='Result returned by the job method (as JSON)'
    )
    
    error_message = fields.Text(
        string='Error Message',
        help='Error message if job failed'
    )
    
    error_traceback = fields.Text(
        string='Error Traceback',
        help='Full error traceback if job failed'
    )
    
    # Performance metrics
    memory_usage = fields.Float(
        string='Memory Usage (MB)',
        help='Peak memory usage during job execution'
    )
    
    cpu_time = fields.Float(
        string='CPU Time (seconds)',
        help='CPU time consumed by job execution'
    )
    
    # Retry information
    retry_count = fields.Integer(
        string='Retry Count', default=0,
        help='Which retry attempt this log represents (0 = first attempt)'
    )
    
    # Additional metadata
    arguments = fields.Text(
        string='Arguments',
        help='Arguments passed to the job method (as JSON)'
    )
    
    keyword_arguments = fields.Text(
        string='Keyword Arguments',
        help='Keyword arguments passed to the job method (as JSON)'
    )

    @classmethod
    async def create_log_entry(cls, job, database_name, worker_process_id=None):
        """Create a log entry for a job execution"""
        log_data = {
            'job_id': job.id,
            'job_uuid': job.uuid,
            'job_name': job.name,
            'database_name': database_name,
            'user_id': job.user_id.id if job.user_id else None,
            'model': job.model,
            'function': job.function,
            'state': 'started',
            'started_at': datetime.now(),
            'worker_process_id': worker_process_id,
            'retry_count': job.retry_count,
            'arguments': job.args,
            'keyword_arguments': job.kwargs,
            'channel_id': job.channel_id.id if job.channel_id else None,
        }
        
        return await cls.create(log_data)

    def mark_completed(self, result=None, performance_metrics=None):
        """Mark log entry as completed"""
        now = datetime.now()
        duration = (now - self.started_at).total_seconds() if self.started_at else 0
        
        update_data = {
            'state': 'completed',
            'finished_at': now,
            'duration': duration
        }
        
        if result is not None:
            try:
                update_data['result'] = json.dumps(result)
            except (TypeError, ValueError):
                update_data['result'] = str(result)
        
        if performance_metrics:
            if 'memory_usage' in performance_metrics:
                update_data['memory_usage'] = performance_metrics['memory_usage']
            if 'cpu_time' in performance_metrics:
                update_data['cpu_time'] = performance_metrics['cpu_time']
        
        self.write(update_data)

    def mark_failed(self, error_message=None, error_traceback=None, performance_metrics=None):
        """Mark log entry as failed"""
        now = datetime.now()
        duration = (now - self.started_at).total_seconds() if self.started_at else 0
        
        update_data = {
            'state': 'failed',
            'finished_at': now,
            'duration': duration
        }
        
        if error_message:
            update_data['error_message'] = str(error_message)
        
        if error_traceback:
            update_data['error_traceback'] = str(error_traceback)
        
        if performance_metrics:
            if 'memory_usage' in performance_metrics:
                update_data['memory_usage'] = performance_metrics['memory_usage']
            if 'cpu_time' in performance_metrics:
                update_data['cpu_time'] = performance_metrics['cpu_time']
        
        self.write(update_data)

    def mark_timeout(self, performance_metrics=None):
        """Mark log entry as timed out"""
        now = datetime.now()
        duration = (now - self.started_at).total_seconds() if self.started_at else 0
        
        update_data = {
            'state': 'timeout',
            'finished_at': now,
            'duration': duration,
            'error_message': 'Job execution timed out'
        }
        
        if performance_metrics:
            if 'memory_usage' in performance_metrics:
                update_data['memory_usage'] = performance_metrics['memory_usage']
            if 'cpu_time' in performance_metrics:
                update_data['cpu_time'] = performance_metrics['cpu_time']
        
        self.write(update_data)

    @classmethod
    def cleanup_old_logs(cls, days=7):
        """Clean up old log entries"""
        cutoff_date = datetime.now() - timedelta(days=days)
        old_logs = cls.search([
            ('started_at', '<', cutoff_date)
        ])
        
        if old_logs:
            count = len(old_logs)
            old_logs.unlink()
            return count
        return 0

    @classmethod
    def get_job_statistics(cls, job_id=None, days=30):
        """Get execution statistics for jobs"""
        domain = [
            ('started_at', '>=', datetime.now() - timedelta(days=days))
        ]
        
        if job_id:
            domain.append(('job_id', '=', job_id))
        
        logs = cls.search(domain)
        
        stats = {
            'total_executions': len(logs),
            'successful_executions': len([l for l in logs if l.state == 'completed']),
            'failed_executions': len([l for l in logs if l.state == 'failed']),
            'timeout_executions': len([l for l in logs if l.state == 'timeout']),
            'average_duration': 0,
            'total_duration': 0,
        }
        
        if logs:
            durations = [l.duration for l in logs if l.duration]
            if durations:
                stats['average_duration'] = sum(durations) / len(durations)
                stats['total_duration'] = sum(durations)
        
        return stats

    @classmethod
    def _system_cleanup_job_logs(cls):
        """System cleanup job for old log entries"""
        import logging
        logger = logging.getLogger(__name__)
        
        # Clean up logs older than 7 days
        cleaned_count = cls.cleanup_old_logs(days=7)
        logger.info(f"Cleaned up {cleaned_count} old job log entries")
        
        return f"Cleaned up {cleaned_count} job log entries"

# The model is automatically registered via the metaclass
