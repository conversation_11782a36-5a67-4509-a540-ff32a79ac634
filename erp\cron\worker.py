"""
Cron Worker Process - Individual worker that executes cron jobs

This module implements the worker process that runs in a separate process
from the main server and executes cron jobs for assigned databases.
"""

import asyncio
import multiprocessing
import signal
import sys
import time
from datetime import datetime
from typing import Dict, List, Set

from ..logging import get_logger


class CronWorker:
    """
    Individual cron worker process that executes jobs for assigned databases.
    
    This worker:
    1. Runs in a separate process from the main server
    2. Manages multiple databases
    3. Schedules and executes cron jobs
    4. Handles job failures and retries
    5. Responds to shutdown signals
    """

    def __init__(self, process_id: int, databases: List[str], config: Dict, shutdown_event: multiprocessing.Event):
        self.process_id = process_id
        self.databases = set(databases)
        self.config = config
        self.shutdown_event = shutdown_event
        
        # Initialize logging for this process
        self.logger = get_logger(f"{__name__}.worker-{process_id}")
        
        # State management
        self.is_running = False
        self.start_time = None
        self.last_job_check = {}  # db_name -> datetime
        
        # Job tracking
        self.active_jobs: Dict[str, Set[str]] = {}  # db_name -> set of job_ids
        self.failed_jobs: Dict[str, Dict[str, int]] = {}  # db_name -> {job_id: retry_count}
        
        # Performance tracking
        self.jobs_executed = 0
        self.jobs_failed = 0
        self.total_execution_time = 0.0

    def run(self):
        """Main entry point for the worker process."""
        try:
            # Setup signal handlers
            self._setup_signal_handlers()
            
            # Initialize the worker
            self._initialize()
            
            # Run the main loop
            asyncio.run(self._main_loop())
            
        except KeyboardInterrupt:
            self.logger.info(f"Worker {self.process_id} received keyboard interrupt")
        except Exception as e:
            self.logger.error(f"Worker {self.process_id} failed: {e}")
            sys.exit(1)
        finally:
            self._cleanup()

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            self.logger.info(f"Worker {self.process_id} received signal {signum}")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

    def _initialize(self):
        """Initialize the worker process."""
        self.logger.info(f"Initializing cron worker {self.process_id} for databases: {list(self.databases)}")
        self.is_running = True
        self.start_time = datetime.now()
        
        # Initialize per-database state
        for db_name in self.databases:
            self.last_job_check[db_name] = datetime.now()
            self.active_jobs[db_name] = set()
            self.failed_jobs[db_name] = {}

    async def _main_loop(self):
        """Main worker loop that checks and executes cron jobs."""
        check_interval = self.config.get('cron_check_interval', 60)
        
        self.logger.info(f"Worker {self.process_id} starting main loop (check interval: {check_interval}s)")
        
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Check for jobs in each database
                for db_name in self.databases:
                    if self.shutdown_event.is_set():
                        break
                    
                    await self._check_database_jobs(db_name)
                
                # Wait for next check or shutdown signal
                for _ in range(check_interval):
                    if self.shutdown_event.is_set():
                        break
                    await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in worker {self.process_id} main loop: {e}")
                await asyncio.sleep(5)  # Brief pause before retrying
        
        self.logger.info(f"Worker {self.process_id} main loop ended")

    async def _check_database_jobs(self, db_name: str):
        """Check for jobs to execute in a specific database."""
        try:
            # Initialize registry for this database
            registry = await self._get_registry(db_name)
            if not registry:
                return
            
            # Get jobs that are ready to run
            jobs = await self._get_jobs_to_run(db_name)
            
            if jobs:
                self.logger.debug(f"Found {len(jobs)} jobs to run in database {db_name}")
                
                for job in jobs:
                    if self.shutdown_event.is_set():
                        break
                    
                    await self._execute_job(db_name, job)
            
            self.last_job_check[db_name] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error checking jobs for database {db_name}: {e}")

    async def _get_registry(self, db_name: str):
        """Get or initialize registry for a database."""
        try:
            from ..database.memory.registry_manager import MemoryRegistryManager
            
            # Initialize registry with delay for cron jobs
            registry = await MemoryRegistryManager.initialize_registry_with_delay(
                db_name, delay_seconds=1.0
            )
            
            return registry
            
        except Exception as e:
            self.logger.error(f"Failed to get registry for database {db_name}: {e}")
            return None

    async def _get_jobs_to_run(self, db_name: str) -> List[Dict]:
        """Get cron jobs that are ready to run for a database."""
        try:
            from ..environment import EnvironmentManager
            
            # Create environment for this database
            env = await EnvironmentManager.create_environment(db_name, 1)  # Use admin user
            
            # Get the ir.cron model
            cron_model = env['ir.cron']
            
            # Get jobs that are ready to run
            jobs = await cron_model.get_jobs_to_run(db_name)
            
            # Convert to list of dictionaries
            job_list = []
            for job in jobs:
                job_data = {
                    'id': job.id,
                    'name': job.name,
                    'model': job.model,
                    'function': job.function,
                    'args': job.args,
                    'user_id': job.user_id,
                    'priority': job.priority,
                    'nextcall': job.nextcall,
                    'numbercall': job.numbercall,
                    'logging_enabled': job.logging_enabled,
                }
                job_list.append(job_data)
            
            return job_list
            
        except Exception as e:
            self.logger.error(f"Failed to get jobs for database {db_name}: {e}")
            return []

    async def _execute_job(self, db_name: str, job: Dict):
        """Execute a single cron job."""
        job_id = job['id']
        job_name = job['name']

        # Check if job is already running
        if job_id in self.active_jobs[db_name]:
            self.logger.debug(f"Job {job_name} ({job_id}) is already running in {db_name}")
            return

        # Add to active jobs
        self.active_jobs[db_name].add(job_id)

        start_time = time.time()
        log_entry = None

        try:
            self.logger.info(f"Executing job {job_name} ({job_id}) in database {db_name}")

            # Create log entry if logging is enabled
            if job.get('logging_enabled', False):
                log_entry = await self._create_log_entry(db_name, job)

            # Execute the job with timeout
            job_timeout = self.config.get('cron_job_timeout', 3600)

            if job_timeout > 0:
                result = await asyncio.wait_for(
                    self._run_job_method(db_name, job),
                    timeout=job_timeout
                )
            else:
                result = await self._run_job_method(db_name, job)

            # Update statistics
            execution_time = time.time() - start_time
            self.jobs_executed += 1
            self.total_execution_time += execution_time

            # Clear from failed jobs if it was previously failing
            if job_id in self.failed_jobs[db_name]:
                del self.failed_jobs[db_name][job_id]

            # Update log entry with success
            if log_entry:
                await log_entry.mark_success(str(result) if result else None)

            self.logger.info(f"Job {job_name} ({job_id}) completed successfully in {execution_time:.2f}s")

        except asyncio.TimeoutError:
            self.logger.error(f"Job {job_name} ({job_id}) timed out after {job_timeout}s")

            # Update log entry with timeout
            if log_entry:
                await log_entry.mark_timeout()

            await self._handle_job_failure(db_name, job, "Timeout")

        except Exception as e:
            self.logger.error(f"Job {job_name} ({job_id}) failed: {e}")

            # Update log entry with failure
            if log_entry:
                import traceback
                error_traceback = traceback.format_exc()
                await log_entry.mark_failed(str(e), error_traceback)

            await self._handle_job_failure(db_name, job, str(e))

        finally:
            # Remove from active jobs
            self.active_jobs[db_name].discard(job_id)

    async def _create_log_entry(self, db_name: str, job: Dict):
        """Create a log entry for job execution if logging is enabled"""
        try:
            from ..environment import EnvironmentManager

            # Create environment for this database
            env = await EnvironmentManager.create_environment(db_name, 1)

            # Get the cron job record
            cron_model = env['ir.cron']
            cron_job = await cron_model.browse(job['id'])

            # Create log entry
            return await cron_job.create_execution_log(
                db_name, str(self.process_id)
            )

        except Exception as e:
            self.logger.error(f"Failed to create log entry for job {job['name']}: {e}")
            return None

    async def _run_job_method(self, db_name: str, job: Dict):
        """Run the actual job method."""
        try:
            from ..environment import EnvironmentManager
            
            # Create environment with the job's user
            user_id = job.get('user_id', 1)
            env = await EnvironmentManager.create_environment(db_name, user_id)
            
            # Get the target model
            model_name = job['model']
            model_obj = env[model_name]
            
            # Get the method
            function_name = job['function']
            method = getattr(model_obj, function_name)
            
            # Parse arguments
            args = []
            kwargs = {}
            if job.get('args'):
                try:
                    import ast
                    parsed_args = ast.literal_eval(job['args'])
                    if isinstance(parsed_args, (list, tuple)):
                        args = list(parsed_args)
                    elif isinstance(parsed_args, dict):
                        kwargs = parsed_args
                except (ValueError, SyntaxError):
                    # If parsing fails, pass args as string
                    args = [job['args']]
            
            # Execute the method
            if asyncio.iscoroutinefunction(method):
                result = await method(*args, **kwargs)
            else:
                result = method(*args, **kwargs)
            
            # Update the job record
            await self._update_job_after_execution(env, job['id'])
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to run job method: {e}")
            raise

    async def _update_job_after_execution(self, env, job_id: str):
        """Update the job record after successful execution."""
        try:
            cron_model = env['ir.cron']
            job = await cron_model.browse([job_id])
            
            if job:
                await job._run_job()  # This updates lastcall, nextcall, and numbercall
                
        except Exception as e:
            self.logger.error(f"Failed to update job {job_id} after execution: {e}")

    async def _handle_job_failure(self, db_name: str, job: Dict, error_message: str):
        """Handle job execution failure."""
        job_id = job['id']
        job_name = job['name']
        
        # Update failure count
        if job_id not in self.failed_jobs[db_name]:
            self.failed_jobs[db_name][job_id] = 0
        
        self.failed_jobs[db_name][job_id] += 1
        self.jobs_failed += 1
        
        retry_count = self.failed_jobs[db_name][job_id]
        max_retries = self.config.get('cron_max_retries', 3)
        
        if retry_count <= max_retries:
            retry_delay = self.config.get('cron_retry_delay', 300)
            self.logger.warning(
                f"Job {job_name} ({job_id}) failed (attempt {retry_count}/{max_retries}). "
                f"Will retry in {retry_delay}s. Error: {error_message}"
            )
            
            # Schedule retry (simplified - in a real implementation, you'd want more sophisticated retry scheduling)
            # For now, we'll just let the next check cycle handle it
            
        else:
            self.logger.error(
                f"Job {job_name} ({job_id}) failed permanently after {max_retries} retries. "
                f"Error: {error_message}"
            )
            # Mark job as failed permanently (could disable it or send alert)

    def _cleanup(self):
        """Cleanup worker resources."""
        self.logger.info(f"Cleaning up worker {self.process_id}")
        self.is_running = False
        
        # Log final statistics
        uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        avg_execution_time = self.total_execution_time / max(self.jobs_executed, 1)
        
        self.logger.info(
            f"Worker {self.process_id} statistics: "
            f"Uptime: {uptime:.1f}s, "
            f"Jobs executed: {self.jobs_executed}, "
            f"Jobs failed: {self.jobs_failed}, "
            f"Average execution time: {avg_execution_time:.2f}s"
        )

    def get_status(self) -> Dict:
        """Get current status of this worker."""
        uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        
        return {
            "process_id": self.process_id,
            "is_running": self.is_running,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "uptime_seconds": uptime,
            "databases": list(self.databases),
            "jobs_executed": self.jobs_executed,
            "jobs_failed": self.jobs_failed,
            "active_jobs_count": sum(len(jobs) for jobs in self.active_jobs.values()),
            "failed_jobs_count": sum(len(jobs) for jobs in self.failed_jobs.values()),
            "last_job_check": {db: check.isoformat() for db, check in self.last_job_check.items()},
        }
