"""
Test cases for the cron system

This module contains tests for:
- Cron expression parsing
- Job scheduling
- Job execution
- Process management
"""

import asyncio
import pytest
from datetime import datetime, timedelta

from erp.cron.utils import CronUtils
from erp.cron.scheduler import CronScheduler
from erp.cron.executor import CronExecutor


class TestCronUtils:
    """Test cron utility functions"""

    def test_parse_cron_expression_valid(self):
        """Test parsing valid cron expressions"""
        # Test basic expression
        result = CronUtils.parse_cron_expression("0 */6 * * *")
        assert result is not None
        minutes, hours, days, months, weekdays = result
        assert minutes == [0]
        assert hours == [0, 6, 12, 18]
        assert len(days) == 31  # All days
        assert len(months) == 12  # All months
        assert len(weekdays) == 7  # All weekdays

    def test_parse_cron_expression_invalid(self):
        """Test parsing invalid cron expressions"""
        # Test invalid expressions
        assert CronUtils.parse_cron_expression("") is None
        assert CronUtils.parse_cron_expression("0 */6 * *") is None  # Missing field
        assert CronUtils.parse_cron_expression("60 */6 * * *") is None  # Invalid minute
        assert CronUtils.parse_cron_expression("0 25 * * *") is None  # Invalid hour

    def test_validate_cron_expression(self):
        """Test cron expression validation"""
        # Valid expressions
        valid, msg = CronUtils.validate_cron_expression("0 */6 * * *")
        assert valid is True
        assert msg == ""

        valid, msg = CronUtils.validate_cron_expression("0 2 * * 0")
        assert valid is True
        assert msg == ""

        # Invalid expressions
        valid, msg = CronUtils.validate_cron_expression("0 */6 * *")
        assert valid is False
        assert "5 fields" in msg

        valid, msg = CronUtils.validate_cron_expression("60 */6 * * *")
        assert valid is False
        assert "minute" in msg.lower()

    def test_calculate_next_run(self):
        """Test calculating next run time"""
        # Test every 6 hours
        from_time = datetime(2024, 1, 1, 0, 0, 0)
        next_run = CronUtils.calculate_next_run("0 */6 * * *", from_time)
        
        assert next_run is not None
        assert next_run.hour in [0, 6, 12, 18]
        assert next_run.minute == 0

    def test_calculate_interval_next_run(self):
        """Test calculating next run time from intervals"""
        base_time = datetime(2024, 1, 1, 12, 0, 0)
        
        # Test hourly interval
        next_run = CronUtils.calculate_interval_next_run(2, "hours", base_time)
        expected = base_time + timedelta(hours=2)
        assert next_run == expected

        # Test daily interval
        next_run = CronUtils.calculate_interval_next_run(1, "days", base_time)
        expected = base_time + timedelta(days=1)
        assert next_run == expected

    def test_get_cron_description(self):
        """Test getting human-readable cron descriptions"""
        # Test daily at 2 AM
        desc = CronUtils.get_cron_description("0 2 * * *")
        assert "02:00" in desc

        # Test every 6 hours
        desc = CronUtils.get_cron_description("0 */6 * * *")
        assert "6" in desc or "hours" in desc.lower()

    def test_is_time_to_run(self):
        """Test checking if it's time to run a job"""
        # Test exact match
        check_time = datetime(2024, 1, 1, 2, 0, 0)  # 2 AM
        assert CronUtils.is_time_to_run("0 2 * * *", check_time) is True

        # Test no match
        check_time = datetime(2024, 1, 1, 3, 0, 0)  # 3 AM
        assert CronUtils.is_time_to_run("0 2 * * *", check_time) is False


class TestCronScheduler:
    """Test cron scheduler functionality"""

    def test_scheduler_initialization(self):
        """Test scheduler initialization"""
        config = {
            'cron_check_interval': 60,
            'cron_enable_missed_jobs': True,
        }
        scheduler = CronScheduler(config)
        
        assert scheduler.check_interval == 60
        assert scheduler.enable_missed_jobs is True
        assert len(scheduler.scheduled_jobs) == 0

    def test_job_ready_check(self):
        """Test checking if a job is ready to run"""
        config = {'cron_check_interval': 60, 'cron_enable_missed_jobs': True}
        scheduler = CronScheduler(config)
        
        current_time = datetime.now()
        past_time = current_time - timedelta(minutes=5)
        future_time = current_time + timedelta(minutes=5)
        
        # Job that should run (past time)
        job_ready = {
            'id': 'test1',
            'active': True,
            'numbercall': -1,
            'nextcall': past_time,
        }
        assert scheduler._is_job_ready(job_ready, current_time) is True
        
        # Job that shouldn't run yet (future time)
        job_not_ready = {
            'id': 'test2',
            'active': True,
            'numbercall': -1,
            'nextcall': future_time,
        }
        assert scheduler._is_job_ready(job_not_ready, current_time) is False
        
        # Inactive job
        job_inactive = {
            'id': 'test3',
            'active': False,
            'numbercall': -1,
            'nextcall': past_time,
        }
        assert scheduler._is_job_ready(job_inactive, current_time) is False


class TestCronExecutor:
    """Test cron job executor functionality"""

    def test_executor_initialization(self):
        """Test executor initialization"""
        config = {
            'cron_job_timeout': 3600,
            'cron_max_retries': 3,
            'cron_retry_delay': 300,
        }
        executor = CronExecutor(config)
        
        assert executor.job_timeout == 3600
        assert executor.max_retries == 3
        assert executor.retry_delay == 300
        assert executor.execution_stats['total_executions'] == 0

    def test_parse_job_arguments(self):
        """Test parsing job arguments"""
        config = {}
        executor = CronExecutor(config)
        
        # Test list arguments
        job_with_list = {'args': '[1, 2, 3]'}
        args, kwargs = executor._parse_job_arguments(job_with_list)
        assert args == [1, 2, 3]
        assert kwargs == {}
        
        # Test dict arguments
        job_with_dict = {'args': "{'key': 'value'}"}
        args, kwargs = executor._parse_job_arguments(job_with_dict)
        assert args == []
        assert kwargs == {'key': 'value'}
        
        # Test string arguments
        job_with_string = {'args': 'test_string'}
        args, kwargs = executor._parse_job_arguments(job_with_string)
        assert args == ['test_string']
        assert kwargs == {}
        
        # Test no arguments
        job_no_args = {}
        args, kwargs = executor._parse_job_arguments(job_no_args)
        assert args == []
        assert kwargs == {}

    def test_execution_statistics(self):
        """Test execution statistics tracking"""
        config = {}
        executor = CronExecutor(config)
        
        # Initial stats
        stats = executor.get_execution_statistics()
        assert stats['total_executions'] == 0
        assert stats['success_rate'] == 0.0
        
        # Simulate some executions
        executor.execution_stats['total_executions'] = 10
        executor.execution_stats['successful_executions'] = 8
        executor.execution_stats['failed_executions'] = 2
        executor.execution_stats['total_execution_time'] = 100.0
        
        stats = executor.get_execution_statistics()
        assert stats['total_executions'] == 10
        assert stats['success_rate'] == 0.8
        assert stats['failure_rate'] == 0.2
        assert stats['average_execution_time'] == 10.0

    def test_reset_statistics(self):
        """Test resetting execution statistics"""
        config = {}
        executor = CronExecutor(config)
        
        # Set some stats
        executor.execution_stats['total_executions'] = 10
        executor.execution_stats['successful_executions'] = 8
        
        # Reset
        executor.reset_statistics()
        
        # Check reset
        stats = executor.get_execution_statistics()
        assert stats['total_executions'] == 0
        assert stats['successful_executions'] == 0


class TestCronIntegration:
    """Integration tests for the cron system"""

    def test_cron_expression_to_next_run_integration(self):
        """Test integration between cron expression parsing and next run calculation"""
        # Test daily at 2 AM
        expr = "0 2 * * *"
        valid, _ = CronUtils.validate_cron_expression(expr)
        assert valid is True
        
        # Calculate next run from midnight
        from_time = datetime(2024, 1, 1, 0, 0, 0)
        next_run = CronUtils.calculate_next_run(expr, from_time)
        
        assert next_run is not None
        assert next_run.hour == 2
        assert next_run.minute == 0
        assert next_run.day == 1  # Same day since it's before 2 AM

    def test_scheduler_executor_integration(self):
        """Test integration between scheduler and executor"""
        # Create scheduler and executor with compatible configs
        config = {
            'cron_check_interval': 60,
            'cron_enable_missed_jobs': True,
            'cron_job_timeout': 3600,
            'cron_max_retries': 3,
            'cron_retry_delay': 300,
        }
        
        scheduler = CronScheduler(config)
        executor = CronExecutor(config)
        
        # Both should be initialized successfully
        assert scheduler.check_interval == 60
        assert executor.job_timeout == 3600
        
        # Test job validation
        test_job = {
            'id': 'test_job',
            'name': 'Test Job',
            'model': 'ir.cron',
            'function': '_test_cron_execution',
            'active': True,
            'numbercall': -1,
            'nextcall': datetime.now() - timedelta(minutes=1),
        }
        
        # Job should be ready according to scheduler
        assert scheduler._is_job_ready(test_job, datetime.now()) is True


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
