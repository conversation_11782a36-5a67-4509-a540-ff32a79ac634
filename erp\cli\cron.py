"""
Cron CLI Commands - Command line interface for cron management

This module provides CLI commands for managing the cron system including:
- Starting/stopping cron processes
- Listing active jobs
- Manually triggering jobs
- Viewing cron status
- Managing cron jobs
"""

import argparse
import asyncio
import json
from datetime import datetime
from typing import Dict, List

from .base import BaseCommand, CommandGroup


class CronCommandGroup(CommandGroup):
    """Command group for cron-related operations"""

    def __init__(self):
        super().__init__()
        self.name = "cron"
        self.description = "Cron job management commands"

    def add_commands(self, subparsers, parent_parser):
        """Add cron commands to the parser"""
        # Register all cron commands
        status_cmd = CronStatusCommand()
        start_cmd = CronStartCommand()
        stop_cmd = CronStopCommand()
        list_cmd = CronListCommand()
        run_cmd = CronRunCommand()
        create_cmd = CronCreateCommand()
        logs_cmd = CronLogsCommand()

        self.register_command(status_cmd)
        self.register_command(start_cmd)
        self.register_command(stop_cmd)
        self.register_command(list_cmd)
        self.register_command(run_cmd)
        self.register_command(create_cmd)
        self.register_command(logs_cmd)

        # Cron status command
        status_parser = subparsers.add_parser(
            "cron-status",
            parents=[parent_parser],
            add_help=False,
            help="Show cron system status"
        )
        status_cmd.add_arguments(status_parser)
        status_parser.set_defaults(command="cron-status")

        # Start cron command
        start_parser = subparsers.add_parser(
            "cron-start",
            parents=[parent_parser],
            add_help=False,
            help="Start cron process manager"
        )
        start_cmd.add_arguments(start_parser)
        start_parser.set_defaults(command="cron-start")

        # Stop cron command
        stop_parser = subparsers.add_parser(
            "cron-stop",
            parents=[parent_parser],
            add_help=False,
            help="Stop cron process manager"
        )
        stop_cmd.add_arguments(stop_parser)
        stop_parser.set_defaults(command="cron-stop")

        # List jobs command
        list_parser = subparsers.add_parser(
            "cron-list",
            parents=[parent_parser],
            add_help=False,
            help="List cron jobs"
        )
        list_cmd.add_arguments(list_parser)
        list_parser.set_defaults(command="cron-list")

        # Run job command
        run_parser = subparsers.add_parser(
            "cron-run",
            parents=[parent_parser],
            add_help=False,
            help="Manually run a cron job"
        )
        run_cmd.add_arguments(run_parser)
        run_parser.set_defaults(command="cron-run")

        # Create job command
        create_parser = subparsers.add_parser(
            "cron-create",
            parents=[parent_parser],
            add_help=False,
            help="Create a new cron job"
        )
        create_cmd.add_arguments(create_parser)
        create_parser.set_defaults(command="cron-create")

        # View logs command
        logs_parser = subparsers.add_parser(
            "cron-logs",
            parents=[parent_parser],
            add_help=False,
            help="View cron job execution logs"
        )
        logs_cmd.add_arguments(logs_parser)
        logs_parser.set_defaults(command="cron-logs")


class CronStatusCommand(BaseCommand):
    """Command to show cron system status"""

    def __init__(self):
        super().__init__()
        self.name = "cron-status"
        self.description = "Show cron system status"

    def add_arguments(self, parser: argparse.ArgumentParser):
        """Add command-specific arguments to parser"""
        # No additional arguments needed for status command
        pass

    def handle(self, args: argparse.Namespace) -> int:
        """Handle the command execution"""
        import asyncio
        return asyncio.run(self._run_async(args))

    async def _run_async(self, args):
        """Show cron system status"""
        try:
            from ..cron.manager import get_cron_manager
            from ..config import config

            self.print_info("Cron System Status")
            self.print_info("=" * 50)

            # Check if cron is enabled
            if not config.cron_config.is_cron_enabled():
                self.print_warning("Cron system is disabled in configuration")
                return 0

            # Get cron manager
            cron_manager = get_cron_manager()
            status = cron_manager.get_status()

            # Display status
            self.print_info(f"Running: {'Yes' if status['is_running'] else 'No'}")
            if status['start_time']:
                self.print_info(f"Started: {status['start_time']}")
            
            self.print_info(f"Worker Processes: {status['worker_processes']}")
            self.print_info(f"Active Processes: {len(status['active_processes'])}")
            
            if status['failed_processes']:
                self.print_warning(f"Failed Processes: {status['failed_processes']}")
            
            self.print_info(f"Managed Databases: {status['managed_databases']}")
            
            if status['database_assignments']:
                self.print_info("\nDatabase Assignments:")
                for db, process_id in status['database_assignments'].items():
                    self.print_info(f"  {db} -> Process {process_id}")

            # Show configuration
            self.print_info("\nConfiguration:")
            for key, value in status['config'].items():
                self.print_info(f"  {key}: {value}")

            return 0

        except Exception as e:
            self.print_error(f"Failed to get cron status: {e}")
            return 1


class CronStartCommand(BaseCommand):
    """Command to start cron process manager"""

    def __init__(self):
        super().__init__()
        self.name = "cron-start"
        self.description = "Start cron process manager"

    def add_arguments(self, parser: argparse.ArgumentParser):
        """Add command-specific arguments to parser"""
        parser.add_argument(
            "--databases", "-d",
            nargs="*",
            help="Specific databases to manage (default: all)"
        )

    def handle(self, args: argparse.Namespace) -> int:
        """Handle the command execution"""
        import asyncio
        return asyncio.run(self._run_async(args))

    async def _run_async(self, args):
        """Start cron process manager"""
        try:
            from ..cron.manager import get_cron_manager
            from ..config import config

            # Check if cron is enabled
            if not config.cron_config.is_cron_enabled():
                self.print_error("Cron system is disabled in configuration")
                return 1

            # Get cron manager
            cron_manager = get_cron_manager()

            if cron_manager.is_running:
                self.print_warning("Cron manager is already running")
                return 0

            # Start cron manager
            databases = args.databases if hasattr(args, 'databases') and args.databases else None
            
            self.print_info("Starting cron process manager...")
            await cron_manager.start(databases)
            
            self.print_success("Cron process manager started successfully")
            
            # Show status
            status = cron_manager.get_status()
            self.print_info(f"Worker processes: {status['worker_processes']}")
            self.print_info(f"Managed databases: {status['managed_databases']}")

            return 0

        except Exception as e:
            self.print_error(f"Failed to start cron manager: {e}")
            return 1


class CronStopCommand(BaseCommand):
    """Command to stop cron process manager"""

    def __init__(self):
        super().__init__()
        self.name = "cron-stop"
        self.description = "Stop cron process manager"

    def add_arguments(self, parser: argparse.ArgumentParser):
        """Add command-specific arguments to parser"""
        # No additional arguments needed for stop command
        pass

    def handle(self, args: argparse.Namespace) -> int:
        """Handle the command execution"""
        import asyncio
        return asyncio.run(self._run_async(args))

    async def _run_async(self, args):
        """Stop cron process manager"""
        try:
            from ..cron.manager import get_cron_manager

            # Get cron manager
            cron_manager = get_cron_manager()

            if not cron_manager.is_running:
                self.print_warning("Cron manager is not running")
                return 0

            # Stop cron manager
            self.print_info("Stopping cron process manager...")
            await cron_manager.stop()
            
            self.print_success("Cron process manager stopped successfully")

            return 0

        except Exception as e:
            self.print_error(f"Failed to stop cron manager: {e}")
            return 1


class CronListCommand(BaseCommand):
    """Command to list cron jobs"""

    def __init__(self):
        super().__init__()
        self.name = "cron-list"
        self.description = "List cron jobs"

    def add_arguments(self, parser: argparse.ArgumentParser):
        """Add command-specific arguments to parser"""
        parser.add_argument(
            "--database", "-d",
            help="Database to list jobs from"
        )
        parser.add_argument(
            "--active-only", "-a",
            action="store_true",
            help="Show only active jobs"
        )
        parser.add_argument(
            "--format", "-f",
            choices=["table", "json"],
            default="table",
            help="Output format"
        )

    def handle(self, args: argparse.Namespace) -> int:
        """Handle the command execution"""
        import asyncio
        return asyncio.run(self._run_async(args))

    async def _run_async(self, args):
        """List cron jobs"""
        try:
            from ..environment import EnvironmentManager

            database = args.database
            if not database:
                self.print_error("Database name is required")
                return 1

            # Create environment
            env = await EnvironmentManager.create_environment(database, 1)
            cron_model = env['ir.cron']

            # Build domain
            domain = []
            if args.active_only:
                domain.append(('active', '=', True))

            # Search for jobs
            jobs = await cron_model.search(domain)

            if not jobs:
                self.print_info("No cron jobs found")
                return 0

            # Format output
            if args.format == "json":
                job_data = []
                for job in jobs:
                    job_data.append({
                        'id': job.id,
                        'name': job.name,
                        'model': job.model,
                        'function': job.function,
                        'active': job.active,
                        'nextcall': job.nextcall.isoformat() if job.nextcall else None,
                        'lastcall': job.lastcall.isoformat() if job.lastcall else None,
                        'cron': job.cron,
                        'interval_number': job.interval_number,
                        'interval_type': job.interval_type,
                    })
                print(json.dumps(job_data, indent=2))
            else:
                # Table format
                self.print_info(f"Cron Jobs in Database: {database}")
                self.print_info("=" * 80)
                
                for job in jobs:
                    status = "Active" if job.active else "Inactive"
                    next_call = job.nextcall.strftime("%Y-%m-%d %H:%M:%S") if job.nextcall else "Not scheduled"
                    
                    self.print_info(f"ID: {job.id}")
                    self.print_info(f"Name: {job.name}")
                    self.print_info(f"Model: {job.model}")
                    self.print_info(f"Function: {job.function}")
                    self.print_info(f"Status: {status}")
                    self.print_info(f"Next Call: {next_call}")
                    
                    if job.cron:
                        self.print_info(f"Cron: {job.cron}")
                    else:
                        self.print_info(f"Interval: Every {job.interval_number} {job.interval_type}")
                    
                    self.print_info("-" * 40)

            return 0

        except Exception as e:
            self.print_error(f"Failed to list cron jobs: {e}")
            return 1


class CronRunCommand(BaseCommand):
    """Command to manually run a cron job"""

    def __init__(self):
        super().__init__()
        self.name = "cron-run"
        self.description = "Manually run a cron job"

    def add_arguments(self, parser: argparse.ArgumentParser):
        """Add command-specific arguments to parser"""
        parser.add_argument(
            "database",
            help="Database containing the job"
        )
        parser.add_argument(
            "job_id",
            help="ID of the job to run"
        )

    def handle(self, args: argparse.Namespace) -> int:
        """Handle the command execution"""
        import asyncio
        return asyncio.run(self._run_async(args))

    async def _run_async(self, args):
        """Manually run a cron job"""
        try:
            from ..environment import EnvironmentManager

            database = args.database
            job_id = args.job_id

            # Create environment
            env = await EnvironmentManager.create_environment(database, 1)
            cron_model = env['ir.cron']

            # Get the job
            job = await cron_model.browse([job_id])
            if not job:
                self.print_error(f"Job {job_id} not found in database {database}")
                return 1

            # Run the job
            self.print_info(f"Running job: {job.name}")
            start_time = datetime.now()
            
            result = await job.button_execute()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.print_success(f"Job completed successfully in {duration:.2f} seconds")
            
            if result:
                self.print_info(f"Result: {result}")

            return 0

        except Exception as e:
            self.print_error(f"Failed to run cron job: {e}")
            return 1


class CronCreateCommand(BaseCommand):
    """Command to create a new cron job"""

    def __init__(self):
        super().__init__()
        self.name = "cron-create"
        self.description = "Create a new cron job"

    def add_arguments(self, parser: argparse.ArgumentParser):
        """Add command-specific arguments to parser"""
        parser.add_argument(
            "database",
            help="Database to create job in"
        )
        parser.add_argument(
            "--name", "-n",
            required=True,
            help="Job name"
        )
        parser.add_argument(
            "--model", "-m",
            required=True,
            help="Target model"
        )
        parser.add_argument(
            "--function", "-f",
            required=True,
            help="Method to call"
        )
        parser.add_argument(
            "--cron", "-c",
            help="Cron expression (e.g., '0 */6 * * *')"
        )
        parser.add_argument(
            "--interval-number",
            type=int,
            default=1,
            help="Interval number (default: 1)"
        )
        parser.add_argument(
            "--interval-type",
            choices=["minutes", "hours", "days", "weeks", "months"],
            default="hours",
            help="Interval type (default: hours)"
        )
        parser.add_argument(
            "--args",
            help="Arguments as Python literal (e.g., '[1, 2]' or '{'key': 'value'}')"
        )

    def handle(self, args: argparse.Namespace) -> int:
        """Handle the command execution"""
        import asyncio
        return asyncio.run(self._run_async(args))

    async def _run_async(self, args):
        """Create a new cron job"""
        try:
            from ..environment import EnvironmentManager

            database = args.database

            # Create environment
            env = await EnvironmentManager.create_environment(database, 1)
            cron_model = env['ir.cron']

            # Prepare job data
            job_data = {
                'name': args.name,
                'model': args.model,
                'function': args.function,
                'interval_number': args.interval_number,
                'interval_type': args.interval_type,
                'active': True,
                'user_id': 1,  # Admin user
            }

            if args.cron:
                job_data['cron'] = args.cron

            if args.args:
                job_data['args'] = args.args

            # Create the job
            job = await cron_model.create(job_data)
            
            self.print_success(f"Cron job created successfully with ID: {job.id}")
            self.print_info(f"Name: {job.name}")
            self.print_info(f"Model: {job.model}")
            self.print_info(f"Function: {job.function}")
            
            if job.cron:
                self.print_info(f"Cron: {job.cron}")
            else:
                self.print_info(f"Interval: Every {job.interval_number} {job.interval_type}")

            return 0

        except Exception as e:
            self.print_error(f"Failed to create cron job: {e}")
            return 1


class CronLogsCommand(BaseCommand):
    """Command to view cron job execution logs"""

    def __init__(self):
        super().__init__()
        self.name = "cron-logs"
        self.description = "View cron job execution logs"

    def add_arguments(self, parser: argparse.ArgumentParser):
        """Add command-specific arguments to parser"""
        parser.add_argument(
            "--database", "-d",
            help="Database to view logs from"
        )
        parser.add_argument(
            "--job-id", "-j",
            help="Show logs for specific job ID"
        )
        parser.add_argument(
            "--job-name", "-n",
            help="Show logs for jobs with specific name"
        )
        parser.add_argument(
            "--status", "-s",
            choices=["running", "success", "failed", "timeout", "cancelled"],
            help="Filter by execution status"
        )
        parser.add_argument(
            "--days", "-D",
            type=int,
            default=7,
            help="Number of days to look back (default: 7)"
        )
        parser.add_argument(
            "--limit", "-l",
            type=int,
            default=50,
            help="Maximum number of logs to show (default: 50)"
        )
        parser.add_argument(
            "--format", "-f",
            choices=["table", "json", "detailed"],
            default="table",
            help="Output format"
        )

    def handle(self, args: argparse.Namespace) -> int:
        """Handle the command execution"""
        import asyncio
        return asyncio.run(self._run_async(args))

    async def _run_async(self, args):
        """View cron job execution logs"""
        try:
            from ..environment import EnvironmentManager
            from datetime import datetime, timedelta

            # Validate database
            if not args.database:
                self.print_error("Database is required")
                return 1

            # Create environment
            env = await EnvironmentManager.create_environment(args.database, 1)
            log_model = env['ir.cron.log']

            # Build domain for filtering
            domain = []

            # Filter by job ID
            if args.job_id:
                domain.append(('cron_id', '=', args.job_id))

            # Filter by job name
            if args.job_name:
                domain.append(('job_name', 'ilike', f'%{args.job_name}%'))

            # Filter by status
            if args.status:
                domain.append(('status', '=', args.status))

            # Filter by date range
            cutoff_date = datetime.now() - timedelta(days=args.days)
            domain.append(('start_time', '>=', cutoff_date))

            # Get logs
            logs = await log_model.search(domain, limit=args.limit, order='start_time desc')

            if not logs:
                self.print_info("No logs found matching the criteria")
                return 0

            # Display logs based on format
            if args.format == "json":
                await self._display_logs_json(logs)
            elif args.format == "detailed":
                await self._display_logs_detailed(logs)
            else:
                await self._display_logs_table(logs)

            return 0

        except Exception as e:
            self.print_error(f"Failed to view cron logs: {e}")
            return 1

    async def _display_logs_table(self, logs):
        """Display logs in table format"""
        self.print_info(f"Found {len(logs)} log entries:")
        self.print_info("")

        # Table header
        header = f"{'ID':<8} {'Job Name':<25} {'Status':<10} {'Start Time':<20} {'Duration':<10}"
        self.print_info(header)
        self.print_info("-" * len(header))

        # Table rows
        for log in logs:
            duration = f"{log.execution_time:.2f}s" if log.execution_time else "N/A"
            start_time = log.start_time.strftime("%Y-%m-%d %H:%M:%S") if log.start_time else "N/A"

            row = f"{str(log.id):<8} {log.job_name[:24]:<25} {log.status:<10} {start_time:<20} {duration:<10}"
            self.print_info(row)

    async def _display_logs_detailed(self, logs):
        """Display logs in detailed format"""
        for i, log in enumerate(logs):
            if i > 0:
                self.print_info("\n" + "="*80 + "\n")

            self.print_info(f"Log ID: {log.id}")
            self.print_info(f"Job: {log.job_name} (ID: {log.cron_id})")
            self.print_info(f"Database: {log.database_name}")
            self.print_info(f"Status: {log.status}")
            self.print_info(f"Model: {log.model_name}")
            self.print_info(f"Function: {log.function_name}")

            if log.start_time:
                self.print_info(f"Start Time: {log.start_time}")
            if log.end_time:
                self.print_info(f"End Time: {log.end_time}")
            if log.execution_time:
                self.print_info(f"Duration: {log.execution_time:.2f} seconds")

            if log.arguments:
                self.print_info(f"Arguments: {log.arguments}")

            if log.result:
                self.print_info(f"Result: {log.result}")

            if log.error_message:
                self.print_info(f"Error: {log.error_message}")

            if log.worker_process_id:
                self.print_info(f"Worker Process: {log.worker_process_id}")

    async def _display_logs_json(self, logs):
        """Display logs in JSON format"""
        log_data = []
        for log in logs:
            log_dict = {
                'id': log.id,
                'cron_id': log.cron_id,
                'job_name': log.job_name,
                'database_name': log.database_name,
                'status': log.status,
                'model_name': log.model_name,
                'function_name': log.function_name,
                'start_time': log.start_time.isoformat() if log.start_time else None,
                'end_time': log.end_time.isoformat() if log.end_time else None,
                'execution_time': log.execution_time,
                'arguments': log.arguments,
                'result': log.result,
                'error_message': log.error_message,
                'worker_process_id': log.worker_process_id,
            }
            log_data.append(log_dict)

        print(json.dumps(log_data, indent=2))
