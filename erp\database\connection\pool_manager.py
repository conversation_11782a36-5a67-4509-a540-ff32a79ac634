"""
Global Database Connection Pool Manager

This module provides a centralized connection pool manager that can be shared
across multiple processes to prevent connection pool exhaustion.
"""

import asyncio
from typing import Dict, Optional
from contextlib import asynccontextmanager

import asyncpg

from ...config import config
from ...logging import get_logger
from .pool import ConnectionPool


class GlobalPoolManager:
    """
    Global connection pool manager that prevents duplicate pool creation
    and manages connection limits across the entire application.
    """
    
    _instance: Optional['GlobalPoolManager'] = None
    _lock = asyncio.Lock()
    _pools: Dict[str, ConnectionPool] = {}
    _logger = get_logger(__name__)
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    async def get_pool(cls, db_name: str) -> ConnectionPool:
        """
        Get or create a connection pool for the specified database.
        
        This method ensures only one pool per database exists globally,
        preventing connection exhaustion.
        """
        async with cls._lock:
            if db_name not in cls._pools:
                cls._logger.debug(f"Creating new global pool for database: {db_name}")
                pool = ConnectionPool(db_name)
                await pool.create_pool()
                cls._pools[db_name] = pool
                cls._logger.info(f"Global pool created for database: {db_name}")
            else:
                cls._logger.debug(f"Reusing existing global pool for database: {db_name}")
            
            return cls._pools[db_name]
    
    @classmethod
    async def close_pool(cls, db_name: str):
        """Close and remove a specific database pool"""
        async with cls._lock:
            if db_name in cls._pools:
                await cls._pools[db_name].close_pool()
                del cls._pools[db_name]
                cls._logger.info(f"Global pool closed for database: {db_name}")
    
    @classmethod
    async def close_all_pools(cls):
        """Close all database pools"""
        async with cls._lock:
            for db_name, pool in cls._pools.items():
                try:
                    await pool.close_pool()
                    cls._logger.info(f"Global pool closed for database: {db_name}")
                except Exception as e:
                    cls._logger.error(f"Error closing pool for {db_name}: {e}")
            
            cls._pools.clear()
            cls._logger.info("All global pools closed")
    
    @classmethod
    def get_pool_count(cls) -> int:
        """Get the number of active pools"""
        return len(cls._pools)
    
    @classmethod
    def get_active_databases(cls) -> list:
        """Get list of databases with active pools"""
        return list(cls._pools.keys())


# Global instance
_global_pool_manager = None


def get_global_pool_manager() -> GlobalPoolManager:
    """Get the global pool manager instance"""
    global _global_pool_manager
    if _global_pool_manager is None:
        _global_pool_manager = GlobalPoolManager()
    return _global_pool_manager
