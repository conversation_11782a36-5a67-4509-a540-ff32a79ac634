"""
Job Manager - Manages job worker processes and coordinates with cron system
"""

import asyncio
import multiprocessing
import signal
from datetime import datetime
from typing import Dict, List, Optional, Set

from ..logging import get_logger
from ..process.coordination import get_process_coordinator
from ..config import config


class JobManager:
    """
    Job manager that spawns and manages job worker processes.
    
    This manager:
    1. Spawns job worker processes based on channel configuration
    2. Distributes channels across worker processes
    3. Monitors worker process health
    4. Handles graceful shutdown
    5. Manages process restart on failures
    6. Coordinates with existing cron system
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.is_running = False
        self.start_time = None
        
        # Process management
        self.worker_processes: Dict[int, multiprocessing.Process] = {}
        self.process_channels: Dict[int, Set[str]] = {}
        self.process_databases: Dict[int, Set[str]] = {}
        self.channel_assignments: Dict[str, int] = {}
        self.database_assignments: Dict[str, int] = {}
        
        # Configuration
        self.config = config.job_config if hasattr(config, 'job_config') else {}
        self.max_job_processes = self.config.get('max_job_processes', 2)
        self.job_spawn_interval = self.config.get('job_spawn_interval', 2.0)
        
        # Shutdown coordination
        self.shutdown_event = multiprocessing.Event()
        
        self.logger.info("Job Manager initialized")

    async def start(self, databases: List[str] = None):
        """Start the job manager and spawn worker processes."""
        # Use process coordination to prevent duplicate managers
        coordinator = get_process_coordinator()

        if not coordinator.coordinate_startup("job_manager_start"):
            self.logger.debug("Job manager startup already in progress, skipping")
            return

        try:
            if self.is_running:
                self.logger.warning("Job Manager is already running")
                return

            self.logger.info("Starting Job Manager")
            self.is_running = True
            self.start_time = datetime.now()
            
            # Discover databases if not provided
            if databases is None:
                databases = await self._discover_databases()
            
            if not databases:
                self.logger.warning("No databases found for job processing")
                return
            
            # Get active channels
            channels = await self._discover_channels(databases)
            
            if not channels:
                self.logger.warning("No active job channels found")
                return
            
            self.logger.info(f"Managing jobs for {len(databases)} databases and {len(channels)} channels")
            
            # Distribute channels and databases across processes
            self._distribute_channels_and_databases(channels, databases)
            
            # Spawn worker processes
            await self._spawn_worker_processes()
            
            # Start monitoring
            await self._start_monitoring()
            
            self.logger.info(f"Job Manager started with {len(self.worker_processes)} worker processes")
            
        except Exception as e:
            self.logger.error("Failed to start Job Manager: %s", e)
            self.is_running = False
            raise
        finally:
            coordinator.finish_startup_coordination("job_manager_start")

    async def stop(self):
        """Stop the job manager and all worker processes."""
        if not self.is_running:
            return

        try:
            self.logger.info("Stopping Job Manager")
            self.is_running = False
            
            # Signal all workers to shutdown
            self.shutdown_event.set()
            
            # Wait for workers to finish gracefully
            shutdown_timeout = self.config.get('shutdown_timeout', 30)
            
            for process_id, process in self.worker_processes.items():
                try:
                    process.join(timeout=shutdown_timeout)
                    if process.is_alive():
                        self.logger.warning(f"Force terminating worker process {process_id}")
                        process.terminate()
                        process.join(timeout=5)
                        if process.is_alive():
                            process.kill()
                except Exception as e:
                    self.logger.error(f"Error stopping worker process {process_id}: {e}")
            
            self.worker_processes.clear()
            self.process_channels.clear()
            self.process_databases.clear()
            self.channel_assignments.clear()
            self.database_assignments.clear()
            
            self.logger.info("Job Manager stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping Job Manager: {e}")

    async def add_database(self, db_name: str):
        """Add a database to job processing."""
        if db_name in self.database_assignments:
            self.logger.debug(f"Database {db_name} already assigned to job processing")
            return

        # Find the process with the least databases
        target_pid = None
        min_databases = float('inf')
        
        for pid, databases in self.process_databases.items():
            if len(databases) < min_databases:
                min_databases = len(databases)
                target_pid = pid
        
        if target_pid is None:
            # No processes available, spawn a new one if under limit
            if len(self.worker_processes) < self.max_job_processes:
                await self._spawn_single_worker(databases=[db_name], channels=['default'])
            else:
                self.logger.warning(f"Cannot add database {db_name}: maximum processes reached")
        else:
            # Assign to existing process
            self.database_assignments[db_name] = target_pid
            self.process_databases[target_pid].add(db_name)
            self.logger.info(f"Assigned database {db_name} to job process {target_pid}")

    async def _discover_databases(self) -> List[str]:
        """Discover available databases."""
        try:
            from ..database.registry import DatabaseRegistry
            
            # Get all configured databases
            databases = await DatabaseRegistry.get_all_databases()
            return list(databases.keys()) if databases else []
            
        except Exception as e:
            self.logger.error(f"Failed to discover databases: {e}")
            return []

    async def _discover_channels(self, databases: List[str]) -> List[str]:
        """Discover active job channels from databases."""
        channels = set()
        
        for db_name in databases:
            try:
                from ..environment import EnvironmentManager
                
                env = await EnvironmentManager.create_environment(db_name, 1)
                channel_model = env['ir.job.channel']
                
                # Get active channels
                active_channels = channel_model.search([('active', '=', True)])
                for channel in active_channels:
                    channels.add(channel.name)
                    
            except Exception as e:
                self.logger.debug(f"Could not get channels from database {db_name}: {e}")
        
        # Ensure we have at least a default channel
        if not channels:
            channels.add('default')
        
        return list(channels)

    def _distribute_channels_and_databases(self, channels: List[str], databases: List[str]):
        """Distribute channels and databases across worker processes."""
        max_processes = min(self.max_job_processes, len(channels))
        
        # Initialize process assignments
        for i in range(max_processes):
            self.process_channels[i] = set()
            self.process_databases[i] = set()
        
        # Distribute channels
        for i, channel in enumerate(channels):
            process_id = i % max_processes
            self.process_channels[process_id].add(channel)
            self.channel_assignments[channel] = process_id
        
        # Distribute databases
        for i, db_name in enumerate(databases):
            process_id = i % max_processes
            self.process_databases[process_id].add(db_name)
            self.database_assignments[db_name] = process_id
        
        self.logger.info(f"Distributed {len(channels)} channels and {len(databases)} databases across {max_processes} processes")

    async def _spawn_worker_processes(self):
        """Spawn all worker processes."""
        spawn_interval = self.job_spawn_interval
        
        for process_id, channels in self.process_channels.items():
            databases = list(self.process_databases.get(process_id, []))
            await self._spawn_single_worker(list(channels), databases, process_id)
            
            # Add delay between spawning processes
            if spawn_interval > 0:
                await asyncio.sleep(spawn_interval)

    async def _spawn_single_worker(self, channels: List[str], databases: List[str], process_id: int = None) -> int:
        """Spawn a single worker process."""
        if process_id is None:
            process_id = max(self.process_channels.keys(), default=-1) + 1
        
        try:
            from .worker import JobWorker
            
            # Create worker process
            worker = JobWorker(
                process_id=process_id,
                databases=databases,
                channels=channels,
                config=self.config,
                shutdown_event=self.shutdown_event
            )
            
            process = multiprocessing.Process(
                target=worker.run,
                name=f"JobWorker-{process_id}",
                daemon=False
            )
            
            process.start()
            
            # Store process info
            self.worker_processes[process_id] = process
            self.process_channels[process_id] = set(channels)
            self.process_databases[process_id] = set(databases)
            
            for channel in channels:
                self.channel_assignments[channel] = process_id
            for db_name in databases:
                self.database_assignments[db_name] = process_id
            
            self.logger.info(f"Spawned job worker process {process_id} (PID: {process.pid}) for channels: {channels}, databases: {databases}")
            return process_id
            
        except Exception as e:
            self.logger.error(f"Failed to spawn job worker process {process_id}: {e}")
            raise

    async def _start_monitoring(self):
        """Start monitoring worker processes."""
        # This could be expanded to include health checks, restart logic, etc.
        self.logger.info("Job worker monitoring started")

    def get_status(self) -> Dict:
        """Get job manager status."""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'worker_processes': len(self.worker_processes),
            'channels': len(self.channel_assignments),
            'databases': len(self.database_assignments),
            'process_info': {
                pid: {
                    'channels': list(channels),
                    'databases': list(self.process_databases.get(pid, [])),
                    'alive': process.is_alive()
                }
                for pid, (channels, process) in 
                zip(self.process_channels.keys(), 
                    zip(self.process_channels.values(), self.worker_processes.values()))
            }
        }


# Global job manager instance
_job_manager = None


def get_job_manager() -> JobManager:
    """Get the global job manager instance."""
    global _job_manager
    if _job_manager is None:
        _job_manager = JobManager()
    return _job_manager
