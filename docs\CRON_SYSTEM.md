# ERP Cron System Documentation

The ERP system includes a comprehensive cron job system for scheduling and executing background tasks. This system runs in separate processes to avoid blocking the main server and supports multiple databases.

## Features

- **Separate Process Management**: Cron jobs run in dedicated worker processes
- **Multi-Database Support**: Manage cron jobs across multiple databases
- **Cron Expression Support**: Standard cron syntax (e.g., `0 */6 * * *`)
- **Interval-Based Scheduling**: Simple interval scheduling (every N minutes/hours/days)
- **Error Handling & Retries**: Automatic retry of failed jobs with configurable limits
- **CLI Management**: Command-line tools for managing cron jobs
- **Process Monitoring**: Health monitoring and automatic restart of failed processes

## Configuration

Cron system configuration is managed in `erp.conf`:

```ini
# Maximum number of cron worker processes
max_cron_processes = 2

# Interval between spawning processes (seconds)
cron_spawn_interval = 5

# Database connection timeout for cron processes (seconds)
cron_db_timeout = 300

# Maximum execution time for individual jobs (seconds)
cron_job_timeout = 3600

# How often to check for new jobs (seconds)
cron_check_interval = 60

# Maximum retries for failed jobs
cron_max_retries = 3

# Delay between retry attempts (seconds)
cron_retry_delay = 300

# Whether to run missed jobs on startup
cron_enable_missed_jobs = true

# Log level for cron messages
cron_log_level = info

# Number of days to keep cron execution logs
cron_log_retention_days = 7

# How often to run log cleanup (hours)
cron_log_cleanup_interval = 24
```

## Cron Job Model

Cron jobs are stored in the `ir.cron` model with the following fields:

- **name**: Human-readable name for the job
- **model**: Target model name (e.g., `res.users`)
- **function**: Method name to call on the model
- **args**: Arguments to pass to the method (as Python literal)
- **active**: Whether the job is active
- **cron**: Cron expression (takes precedence over interval settings)
- **interval_number**: Number of intervals (e.g., 6 for "every 6 hours")
- **interval_type**: Type of interval (minutes, hours, days, weeks, months)
- **numbercall**: Number of times to run (-1 for unlimited)
- **doall**: Whether to run missed occurrences on restart
- **priority**: Job priority (0=highest, 10=lowest)
- **user_id**: User context for job execution
- **nextcall**: Next scheduled execution time
- **lastcall**: Last execution time
- **logging_enabled**: Whether to enable detailed execution logging

## Cron Expression Syntax

The system supports standard cron expressions with 5 fields:

```
* * * * *
│ │ │ │ │
│ │ │ │ └─── Weekday (0-6, Sunday=0)
│ │ │ └───── Month (1-12)
│ │ └─────── Day of month (1-31)
│ └───────── Hour (0-23)
└─────────── Minute (0-59)
```

### Examples

- `0 2 * * *` - Daily at 2:00 AM
- `0 */6 * * *` - Every 6 hours
- `30 1 * * 0` - Weekly on Sunday at 1:30 AM
- `0 0 1 * *` - Monthly on the 1st at midnight
- `*/15 * * * *` - Every 15 minutes

### Special Characters

- `*` - Any value
- `*/N` - Every N units
- `N-M` - Range from N to M
- `N,M,O` - Specific values N, M, and O

## CLI Commands

### View Cron Status

```bash
erp-bin cron-status
```

Shows the current status of the cron system including running processes and configuration.

### Start Cron Manager

```bash
erp-bin cron-start
erp-bin cron-start --databases db1 db2  # Specific databases only
```

Starts the cron process manager. If already running, shows a warning.

### Stop Cron Manager

```bash
erp-bin cron-stop
```

Stops the cron process manager and all worker processes.

### List Cron Jobs

```bash
erp-bin cron-list --database mydb
erp-bin cron-list --database mydb --active-only
erp-bin cron-list --database mydb --format json
```

Lists cron jobs in a specific database.

### Run Job Manually

```bash
erp-bin cron-run mydb job_id
```

Manually executes a specific cron job for testing purposes.

### View Cron Logs

```bash
erp-bin cron-logs --database mydb
erp-bin cron-logs --database mydb --job-name "Daily Cleanup"
erp-bin cron-logs --database mydb --status failed --days 3
erp-bin cron-logs --database mydb --format json --limit 100
```

Views cron job execution logs with various filtering options.

### Create New Job

```bash
erp-bin cron-create mydb --name "Daily Cleanup" --model "ir.cron" --function "_system_cleanup" --cron "0 2 * * *"
erp-bin cron-create mydb --name "Hourly Task" --model "res.users" --function "cleanup_sessions" --interval-number 1 --interval-type hours
```

Creates a new cron job in the specified database.

## Cron Job Execution Logging

The ERP cron system includes an optional detailed logging feature that tracks individual job executions in a database table. This provides comprehensive audit trails and debugging information for cron jobs.

### Enabling Logging

Logging is disabled by default for performance reasons. To enable logging for a specific cron job:

1. **Via Web Interface**: Set the "Enable Logging" field to True when creating or editing a cron job
2. **Via Code**: Set `logging_enabled=True` when creating a cron job programmatically
3. **Via CLI**: Use the `--logging-enabled` flag when creating jobs with `erp-bin cron-create`

### Log Data Model

Execution logs are stored in the `ir.cron.log` model with the following information:

- **Job Identification**: Job ID, name, database, model, and function
- **Execution Timing**: Start time, end time, and total execution duration
- **Status Tracking**: Running, success, failed, timeout, or cancelled
- **Results**: Method return value and error details with full traceback
- **Context**: User ID, worker process ID, arguments, and retry count

### Automatic Cleanup

Cron logs are automatically cleaned up to prevent database bloat:

- **Retention Period**: Configurable via `cron_log_retention_days` (default: 7 days)
- **Cleanup Frequency**: Configurable via `cron_log_cleanup_interval` (default: 24 hours)
- **Transient Model**: Uses the TransientModel pattern for automatic cleanup

### Viewing Logs

Use the `erp-bin cron-logs` command to view execution logs:

```bash
# View all logs for a database
erp-bin cron-logs --database mydb

# Filter by job name
erp-bin cron-logs --database mydb --job-name "Daily Cleanup"

# Filter by status
erp-bin cron-logs --database mydb --status failed

# View detailed information
erp-bin cron-logs --database mydb --format detailed

# Export as JSON
erp-bin cron-logs --database mydb --format json

# Limit results and time range
erp-bin cron-logs --database mydb --days 3 --limit 50
```

### Performance Considerations

- **Selective Logging**: Only enable logging for jobs that need detailed tracking
- **Retention Policy**: Adjust retention period based on your audit requirements
- **Database Impact**: Logging adds minimal overhead but uses additional storage
- **Cleanup Monitoring**: Monitor cleanup jobs to ensure old logs are removed

## Programming Interface

### Creating Cron Jobs Programmatically

```python
# Create environment
env = await EnvironmentManager.create_environment('mydb', 1)
cron_model = env['ir.cron']

# Create a new job
job_data = {
    'name': 'My Custom Job',
    'model': 'my.model',
    'function': 'my_method',
    'cron': '0 */4 * * *',  # Every 4 hours
    'active': True,
    'user_id': 1,
}

job = await cron_model.create(job_data)
```

### Implementing Cron Job Methods

```python
class MyModel(Model):
    _name = 'my.model'
    
    def my_cron_method(self):
        """Method called by cron job"""
        # Your cron job logic here
        self.logger.info("Cron job executed")
        return "Job completed successfully"
    
    @classmethod
    def my_class_method(cls):
        """Class method for cron jobs"""
        # Use class methods for jobs that don't need instance data
        return "Class method executed"
```

### Job Arguments

You can pass arguments to cron job methods:

```python
# List arguments
job_data['args'] = '[1, 2, "hello"]'

# Dictionary arguments
job_data['args'] = '{"param1": "value1", "param2": 42}'

# Single argument
job_data['args'] = '"single_string_argument"'
```

## Default Cron Jobs

The base addon includes several default cron jobs:

1. **System Cleanup** - Daily at 2 AM
   - Cleans up old logs and temporary files
   - Active by default

2. **Database Maintenance** - Weekly on Sunday at 3 AM
   - Performs database maintenance tasks
   - Inactive by default

3. **Session Cleanup** - Every 6 hours
   - Removes expired user sessions
   - Active by default

4. **Registry Refresh** - Every hour
   - Refreshes model registry cache
   - Inactive by default

5. **Test Job** - Every 5 minutes (10 times)
   - For testing cron functionality
   - Inactive by default

## Process Architecture

The cron system uses a multi-process architecture:

1. **Main Server Process**: Runs the web server and API
2. **Cron Manager Process**: Manages worker processes and job distribution
3. **Worker Processes**: Execute actual cron jobs

This separation ensures that:
- Long-running cron jobs don't block the web server
- Failed cron jobs don't crash the main application
- Multiple jobs can run in parallel
- The system can scale across multiple databases

## Error Handling

The cron system includes comprehensive error handling:

- **Timeouts**: Jobs that exceed the configured timeout are terminated
- **Retries**: Failed jobs are automatically retried with exponential backoff
- **Logging**: All job executions and errors are logged
- **Process Recovery**: Failed worker processes are automatically restarted

## Monitoring and Debugging

### Log Messages

Cron activities are logged with the configured log level:

```
INFO: Executing job Daily Cleanup (job_123) in database mydb
INFO: Job Daily Cleanup (job_123) completed successfully in 2.34s
ERROR: Job Failed Task (job_456) failed: Division by zero
WARNING: Job Slow Task (job_789) timed out after 3600s
```

### Performance Monitoring

The system tracks execution statistics:
- Total jobs executed
- Success/failure rates
- Average execution times
- Timeout occurrences

### Health Checks

Use `erp-bin cron-status` to monitor system health:
- Process status
- Database assignments
- Failed processes
- Configuration settings

## Best Practices

1. **Keep Jobs Short**: Design jobs to complete quickly to avoid timeouts
2. **Handle Errors Gracefully**: Include proper error handling in job methods
3. **Use Appropriate Scheduling**: Choose between cron expressions and intervals based on needs
4. **Monitor Performance**: Regularly check job execution times and success rates
5. **Test Thoroughly**: Use the manual execution feature to test jobs before scheduling
6. **Resource Management**: Be mindful of database connections and memory usage in jobs
7. **Logging**: Include appropriate logging in job methods for debugging

## Troubleshooting

### Common Issues

1. **Jobs Not Running**
   - Check if cron system is enabled in configuration
   - Verify cron manager is started
   - Check job is active and has valid next call time

2. **Jobs Failing**
   - Check job method exists on target model
   - Verify arguments are valid Python literals
   - Check user permissions for job execution

3. **Performance Issues**
   - Monitor job execution times
   - Check for database connection limits
   - Consider reducing job frequency or splitting large jobs

4. **Process Issues**
   - Check system resources (CPU, memory)
   - Monitor process logs for errors
   - Restart cron manager if processes are failing

### Debug Commands

```bash
# Check system status
erp-bin cron-status

# List all jobs
erp-bin cron-list --database mydb

# Test job manually
erp-bin cron-run mydb job_id

# Check logs
tail -f logs/erp.log | grep -i cron
```
