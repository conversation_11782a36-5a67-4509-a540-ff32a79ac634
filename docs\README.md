# ERP-PY Documentation

Welcome to the ERP-PY documentation! This directory contains comprehensive documentation for the modern Odoo-inspired ERP system.

## 📚 Documentation Index

### Getting Started
- **[Development Guide](DEVELOPMENT_GUIDE.md)** - Complete setup and development workflow
- **[Quick Start](../README.md#quick-start)** - Fast setup for immediate use
- **[ERP-BIN Reference](ERP_BIN_REFERENCE.md)** - Command-line interface documentation

### Architecture & Design
- **[System Architecture](ARCHITECTURE.md)** - Core system design and components
- **[Technical Reference](TECHNICAL_REFERENCE.md)** - Detailed technical specifications
- **[Addon System](ADDON_SYSTEM.md)** - Addon architecture and development

### Advanced Topics
- **[Database Validation](DATABASE_VALIDATION.md)** - Database validation system
- **[Dependency Management](DEPENDENCY_SAVEPOINT_SYSTEM.md)** - Dependency savepoint system
- **[Deployment Guide](DEPLOYMENT.md)** - Production deployment instructions

## 🎯 Quick Navigation

### For Developers
1. Start with [Development Guide](DEVELOPMENT_GUIDE.md) for setup
2. Read [System Architecture](ARCHITECTURE.md) for understanding
3. Explore [Addon System](ADDON_SYSTEM.md) for addon development
4. Reference [Technical Reference](TECHNICAL_REFERENCE.md) for APIs

### For System Administrators
1. Review [Deployment Guide](DEPLOYMENT.md) for production setup
2. Use [ERP-BIN Reference](ERP_BIN_REFERENCE.md) for command-line operations
3. Check [Database Validation](DATABASE_VALIDATION.md) for maintenance

### For Contributors
1. Follow [Development Guide](DEVELOPMENT_GUIDE.md) for environment setup
2. Understand [System Architecture](ARCHITECTURE.md) before contributing
3. Review [Technical Reference](TECHNICAL_REFERENCE.md) for coding standards

## 📖 Documentation Standards

All documentation follows these standards:
- **Clear Structure**: Logical organization with proper headings
- **Code Examples**: Practical examples for all concepts
- **Cross-References**: Links between related documentation
- **Up-to-Date**: Regular updates to match codebase changes

## 🔄 Recent Updates

See [CHANGELOG.md](../CHANGELOG.md) for recent documentation changes and improvements.

## 🤝 Contributing to Documentation

Documentation improvements are welcome! Please:
1. Keep content clear and concise
2. Include practical examples
3. Update cross-references when adding new content
4. Follow the existing structure and style
