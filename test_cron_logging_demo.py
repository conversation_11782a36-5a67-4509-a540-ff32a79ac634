#!/usr/bin/env python3
"""
Demo script to test the cron logging functionality

This script demonstrates the new cron logging feature by:
1. Creating a test cron job with logging enabled
2. Simulating job execution with logging
3. Viewing the logs
4. Testing cleanup functionality
"""

import asyncio
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from erp.environment import EnvironmentManager


async def demo_cron_logging():
    """Demonstrate cron logging functionality"""
    print("🔧 Cron Logging Feature Demo")
    print("=" * 50)
    
    try:
        # Create environment
        print("📊 Creating test environment...")
        env = await EnvironmentManager.create_environment("demo_db", 1)
        
        # Get models
        cron_model = env['ir.cron']
        log_model = env['ir.cron.log']
        
        print("✅ Environment created successfully")
        
        # 1. Create a test cron job with logging enabled
        print("\n📝 Creating test cron job with logging enabled...")
        job_data = {
            'name': 'Demo Logging Job',
            'model': 'ir.cron',
            'function': '_test_cron_execution',
            'active': True,
            'logging_enabled': True,  # Enable logging
            'interval_number': 1,
            'interval_type': 'hours',
            'user_id': 1,
            'priority': 5,
            'description': 'Demo job to test logging functionality'
        }
        
        cron_job = await cron_model.create(job_data)
        print(f"✅ Created cron job: {cron_job.name} (ID: {cron_job.id})")
        print(f"   Logging enabled: {cron_job.logging_enabled}")
        
        # 2. Simulate job execution with logging
        print("\n🚀 Simulating job execution with logging...")
        
        # Create log entry
        log_entry = await cron_job.create_execution_log("demo_db", "demo_worker_123")
        if log_entry:
            print(f"✅ Created log entry: {log_entry.id}")
            print(f"   Status: {log_entry.status}")
            print(f"   Start time: {log_entry.start_time}")
            
            # Simulate some work
            await asyncio.sleep(0.1)
            
            # Mark as successful
            await log_entry.mark_success("Demo job completed successfully")
            print(f"✅ Marked job as successful")
            print(f"   End time: {log_entry.end_time}")
            print(f"   Execution time: {log_entry.execution_time:.3f}s")
            print(f"   Result: {log_entry.result}")
        else:
            print("❌ No log entry created (logging might be disabled)")
        
        # 3. Create additional log entries for testing
        print("\n📊 Creating additional test log entries...")
        
        # Success log
        success_log = await log_model.create_log_entry(cron_job, "demo_db", "worker_1")
        await asyncio.sleep(0.05)
        await success_log.mark_success("Another successful execution")
        
        # Failed log
        failed_log = await log_model.create_log_entry(cron_job, "demo_db", "worker_2")
        await asyncio.sleep(0.03)
        await failed_log.mark_failed("Simulated error", "Traceback: Demo error for testing")
        
        # Timeout log
        timeout_log = await log_model.create_log_entry(cron_job, "demo_db", "worker_3")
        await timeout_log.mark_timeout()
        
        print("✅ Created 3 additional log entries (success, failed, timeout)")
        
        # 4. View logs
        print("\n📋 Viewing cron execution logs...")
        
        # Get all logs for this job
        logs = await log_model.search([('cron_id', '=', cron_job.id)], order='start_time desc')
        
        print(f"Found {len(logs)} log entries:")
        print("-" * 80)
        print(f"{'ID':<8} {'Status':<10} {'Start Time':<20} {'Duration':<10} {'Result':<20}")
        print("-" * 80)
        
        for log in logs:
            duration = f"{log.execution_time:.3f}s" if log.execution_time else "N/A"
            start_time = log.start_time.strftime("%H:%M:%S") if log.start_time else "N/A"
            result = (log.result or log.error_message or "N/A")[:18]
            
            print(f"{str(log.id):<8} {log.status:<10} {start_time:<20} {duration:<10} {result:<20}")
        
        # 5. Get statistics
        print("\n📈 Job execution statistics:")
        stats = await log_model.get_job_statistics(cron_job.id, days=1)
        
        print(f"   Total executions: {stats['total_executions']}")
        print(f"   Successful: {stats['successful']}")
        print(f"   Failed: {stats['failed']}")
        print(f"   Timeouts: {stats['timeouts']}")
        print(f"   Success rate: {stats['success_rate']:.1f}%")
        print(f"   Average execution time: {stats['average_execution_time']:.3f}s")
        
        # 6. Test cleanup functionality
        print("\n🧹 Testing log cleanup functionality...")
        
        # Create an old log entry
        old_log_data = {
            'cron_id': cron_job.id,
            'job_name': cron_job.name,
            'database_name': 'demo_db',
            'start_time': datetime.now() - timedelta(days=10),  # 10 days old
            'end_time': datetime.now() - timedelta(days=10),
            'status': 'success',
            'model_name': cron_job.model,
            'function_name': cron_job.function,
            'user_id': 1,
            'execution_time': 1.0,
        }
        old_log = await log_model.create(old_log_data)
        print(f"✅ Created old log entry (10 days old): {old_log.id}")
        
        # Count logs before cleanup
        all_logs_before = await log_model.search([('cron_id', '=', cron_job.id)])
        print(f"   Logs before cleanup: {len(all_logs_before)}")
        
        # Run cleanup (7 day retention)
        await log_model.cleanup_old_logs(max_age_hours=7*24)
        
        # Count logs after cleanup
        all_logs_after = await log_model.search([('cron_id', '=', cron_job.id)])
        print(f"   Logs after cleanup: {len(all_logs_after)}")
        print(f"   Cleaned up: {len(all_logs_before) - len(all_logs_after)} old logs")
        
        # 7. Test job without logging
        print("\n🔇 Testing job without logging...")
        job_no_logging_data = {
            'name': 'Demo Job No Logging',
            'model': 'ir.cron',
            'function': '_test_cron_execution',
            'active': True,
            'logging_enabled': False,  # Disable logging
            'interval_number': 1,
            'interval_type': 'hours',
            'user_id': 1,
            'priority': 5,
        }
        
        job_no_logging = await cron_model.create(job_no_logging_data)
        log_entry_disabled = await job_no_logging.create_execution_log("demo_db", "worker_test")
        
        if log_entry_disabled is None:
            print("✅ No log entry created for job with logging disabled")
        else:
            print("❌ Log entry was created even though logging is disabled")
        
        print("\n🎉 Demo completed successfully!")
        print("\nTo view logs via CLI, you can use:")
        print(f"   erp-bin cron-logs --database demo_db --job-id {cron_job.id}")
        print(f"   erp-bin cron-logs --database demo_db --job-name 'Demo Logging Job'")
        print(f"   erp-bin cron-logs --database demo_db --status failed")
        print(f"   erp-bin cron-logs --database demo_db --format json")
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(demo_cron_logging())
    sys.exit(exit_code)
