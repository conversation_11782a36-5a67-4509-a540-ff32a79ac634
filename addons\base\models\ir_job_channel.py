"""
Job Channel model for managing parallel job execution
"""
import os
from datetime import datetime

# Import base model
from erp.models import Model
from erp import fields

# Ensure this addon is available via standardized import
from erp.addons import ensure_addon_import
addon_path = os.path.join(os.path.dirname(__file__), '..')
ensure_addon_import('base', addon_path)


class IrJobChannel(Model):
    """Model for managing job execution channels"""

    _name = 'ir.job.channel'
    _description = 'Job Execution Channels'

    # Define SQL constraints for data integrity
    _sql_constraints = [
        ('unique_name', 'UNIQUE (name)', 'Channel name must be unique'),
        ('check_capacity_positive',
         'CHECK (capacity > 0)',
         'Channel capacity must be positive'),
        ('check_priority_range',
         'CHECK (priority >= 0 AND priority <= 10)',
         'Priority must be between 0 and 10'),
    ]

    # Basic channel information
    name = fields.Char(
        string='Channel Name', required=True, index=True,
        help='Name of the job execution channel'
    )
    
    active = fields.Boolean(
        string='Active', default=True, required=True, index=True,
        help='Whether this channel is active and can process jobs'
    )
    
    description = fields.Text(
        string='Description',
        help='Description of what this channel is used for'
    )
    
    # Channel configuration
    capacity = fields.Integer(
        string='Worker Capacity', default=1, required=True,
        help='Maximum number of parallel jobs this channel can process'
    )
    
    priority = fields.Integer(
        string='Channel Priority', default=5, required=True,
        help='Priority of this channel (0=highest, 10=lowest)'
    )
    
    # Database assignment
    database_pattern = fields.Char(
        string='Database Pattern',
        help='Pattern to match database names (e.g., "prod_*", "test_db"). '
             'Leave empty to process jobs from all databases.'
    )
    
    # Job filtering
    model_pattern = fields.Char(
        string='Model Pattern',
        help='Pattern to match model names (e.g., "sale.*", "account.invoice"). '
             'Leave empty to process jobs from all models.'
    )
    
    function_pattern = fields.Char(
        string='Function Pattern',
        help='Pattern to match function names (e.g., "process_*", "send_email"). '
             'Leave empty to process all functions.'
    )
    
    # Execution control
    max_execution_time = fields.Integer(
        string='Max Execution Time (seconds)', default=3600,
        help='Maximum time a job can run before being terminated (0 = no limit)'
    )
    
    retry_count = fields.Integer(
        string='Retry Count', default=3,
        help='Number of times to retry failed jobs'
    )
    
    retry_delay = fields.Integer(
        string='Retry Delay (seconds)', default=300,
        help='Delay between retry attempts'
    )
    
    # Statistics and monitoring
    jobs_processed = fields.Integer(
        string='Jobs Processed', default=0, readonly=True,
        help='Total number of jobs processed by this channel'
    )
    
    jobs_failed = fields.Integer(
        string='Jobs Failed', default=0, readonly=True,
        help='Total number of jobs that failed in this channel'
    )
    
    last_job_at = fields.Datetime(
        string='Last Job Execution', readonly=True,
        help='Timestamp of the last job execution in this channel'
    )
    
    # Worker process tracking
    worker_pids = fields.Text(
        string='Worker Process IDs', readonly=True,
        help='JSON list of worker process IDs assigned to this channel'
    )

    def matches_job(self, job_data):
        """Check if a job matches this channel's criteria"""
        import re
        
        # Check database pattern
        if self.database_pattern:
            db_name = job_data.get('database_name', '')
            if not re.match(self.database_pattern.replace('*', '.*'), db_name):
                return False
        
        # Check model pattern
        if self.model_pattern:
            model_name = job_data.get('model', '')
            if not re.match(self.model_pattern.replace('*', '.*'), model_name):
                return False
        
        # Check function pattern
        if self.function_pattern:
            function_name = job_data.get('function', '')
            if not re.match(self.function_pattern.replace('*', '.*'), function_name):
                return False
        
        return True

    def get_available_capacity(self):
        """Get the number of available worker slots in this channel"""
        # This would be implemented to check current running jobs
        # For now, return the full capacity
        return self.capacity

    def increment_job_stats(self, success=True):
        """Increment job statistics"""
        if success:
            self.write({
                'jobs_processed': self.jobs_processed + 1,
                'last_job_at': datetime.now()
            })
        else:
            self.write({
                'jobs_failed': self.jobs_failed + 1,
                'last_job_at': datetime.now()
            })

    @classmethod
    def get_best_channel_for_job(cls, job_data):
        """Find the best channel for executing a job"""
        # Get all active channels ordered by priority
        channels = cls.search([
            ('active', '=', True)
        ], order='priority ASC, capacity DESC')
        
        # Find channels that match the job criteria and have capacity
        for channel in channels:
            if channel.matches_job(job_data) and channel.get_available_capacity() > 0:
                return channel
        
        # If no specific channel matches, try to find a general channel
        general_channels = cls.search([
            ('active', '=', True),
            ('database_pattern', '=', False),
            ('model_pattern', '=', False),
            ('function_pattern', '=', False)
        ], order='priority ASC, capacity DESC')
        
        for channel in general_channels:
            if channel.get_available_capacity() > 0:
                return channel
        
        return None

    @classmethod
    def create_default_channels(cls):
        """Create default job channels"""
        default_channels = [
            {
                'name': 'default',
                'description': 'Default job execution channel',
                'capacity': 2,
                'priority': 5,
                'active': True,
            },
            {
                'name': 'high_priority',
                'description': 'High priority job execution channel',
                'capacity': 1,
                'priority': 1,
                'active': True,
            },
            {
                'name': 'background',
                'description': 'Background job execution channel',
                'capacity': 4,
                'priority': 8,
                'active': True,
            }
        ]
        
        for channel_data in default_channels:
            # Check if channel already exists
            existing = cls.search([('name', '=', channel_data['name'])])
            if not existing:
                cls.create(channel_data)

# The model is automatically registered via the metaclass
