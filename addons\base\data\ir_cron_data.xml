<?xml version="1.0" encoding="utf-8"?>
<erp>
    <data>
        <!-- Default cron jobs for the base addon -->
        
        <!-- System cleanup job - runs daily at 2 AM -->
        <record id="ir_cron_system_cleanup" model="ir.cron">
            <field name="name">System Cleanup</field>
            <field name="model">ir.cron</field>
            <field name="function">_system_cleanup</field>
            <field name="cron">0 2 * * *</field>
            <field name="active">True</field>
            <field name="priority">5</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="numbercall">-1</field>
            <field name="doall">True</field>
            <field name="description">Daily system cleanup job that removes old logs and temporary files</field>
        </record>
        
        <!-- Database maintenance job - runs weekly on Sunday at 3 AM -->
        <record id="ir_cron_db_maintenance" model="ir.cron">
            <field name="name">Database Maintenance</field>
            <field name="model">ir.cron</field>
            <field name="function">_database_maintenance</field>
            <field name="cron">0 3 * * 0</field>
            <field name="active">False</field>
            <field name="priority">3</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="numbercall">-1</field>
            <field name="doall">True</field>
            <field name="description">Weekly database maintenance including vacuum and analyze operations</field>
        </record>
        
        <!-- Session cleanup job - runs every 6 hours -->
        <record id="ir_cron_session_cleanup" model="ir.cron">
            <field name="name">Session Cleanup</field>
            <field name="model">res.users</field>
            <field name="function">cleanup_expired_sessions</field>
            <field name="interval_number">6</field>
            <field name="interval_type">hours</field>
            <field name="active">True</field>
            <field name="priority">7</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="numbercall">-1</field>
            <field name="doall">False</field>
            <field name="description">Cleanup expired user sessions every 6 hours</field>
        </record>
        
        <!-- Registry refresh job - runs every hour (disabled by default) -->
        <record id="ir_cron_registry_refresh" model="ir.cron">
            <field name="name">Registry Refresh</field>
            <field name="model">ir.model</field>
            <field name="function">refresh_registry_cache</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="active">False</field>
            <field name="priority">8</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="numbercall">-1</field>
            <field name="doall">False</field>
            <field name="description">Refresh model registry cache to ensure consistency</field>
        </record>
        
        <!-- Test cron job - runs every 5 minutes (for testing purposes) -->
        <record id="ir_cron_test_job" model="ir.cron">
            <field name="name">Test Cron Job</field>
            <field name="model">ir.cron</field>
            <field name="function">_test_cron_execution</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="active">False</field>
            <field name="priority">9</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="numbercall">10</field>
            <field name="doall">False</field>
            <field name="description">Test cron job that runs 10 times every 5 minutes for testing purposes</field>
        </record>

        <!-- Cron log cleanup job - runs daily at 3 AM (active by default) -->
        <record id="ir_cron_log_cleanup" model="ir.cron">
            <field name="name">Cron Log Cleanup</field>
            <field name="model">ir.cron.log</field>
            <field name="function">cleanup_old_logs</field>
            <field name="cron">0 3 * * *</field>
            <field name="active">True</field>
            <field name="priority">9</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="numbercall">-1</field>
            <field name="doall">True</field>
            <field name="description">Daily cleanup of old cron execution logs based on retention policy</field>
        </record>

    </data>
</erp>
