<?xml version="1.0" encoding="utf-8"?>
<erp>
    <data>
        <!-- Default Job Channel -->
        <record id="job_channel_default" model="ir.job.channel">
            <field name="name">default</field>
            <field name="description">Default job execution channel for general purpose jobs</field>
            <field name="capacity">2</field>
            <field name="priority">5</field>
            <field name="active">True</field>
            <field name="max_execution_time">3600</field>
            <field name="retry_count">3</field>
            <field name="retry_delay">300</field>
        </record>

        <!-- High Priority Job Channel -->
        <record id="job_channel_high_priority" model="ir.job.channel">
            <field name="name">high_priority</field>
            <field name="description">High priority job execution channel for urgent tasks</field>
            <field name="capacity">1</field>
            <field name="priority">1</field>
            <field name="active">True</field>
            <field name="max_execution_time">1800</field>
            <field name="retry_count">5</field>
            <field name="retry_delay">60</field>
        </record>

        <!-- Background Job Channel -->
        <record id="job_channel_background" model="ir.job.channel">
            <field name="name">background</field>
            <field name="description">Background job execution channel for low priority, long-running tasks</field>
            <field name="capacity">4</field>
            <field name="priority">8</field>
            <field name="active">True</field>
            <field name="max_execution_time">7200</field>
            <field name="retry_count">2</field>
            <field name="retry_delay">600</field>
        </record>

        <!-- Email Job Channel -->
        <record id="job_channel_email" model="ir.job.channel">
            <field name="name">email</field>
            <field name="description">Dedicated channel for email sending jobs</field>
            <field name="capacity">2</field>
            <field name="priority">3</field>
            <field name="active">True</field>
            <field name="function_pattern">send_email*</field>
            <field name="max_execution_time">600</field>
            <field name="retry_count">3</field>
            <field name="retry_delay">120</field>
        </record>

        <!-- Report Generation Channel -->
        <record id="job_channel_reports" model="ir.job.channel">
            <field name="name">reports</field>
            <field name="description">Channel for report generation and data processing jobs</field>
            <field name="capacity">1</field>
            <field name="priority">6</field>
            <field name="active">True</field>
            <field name="function_pattern">generate_report*</field>
            <field name="max_execution_time">3600</field>
            <field name="retry_count">2</field>
            <field name="retry_delay">300</field>
        </record>
    </data>
</erp>
