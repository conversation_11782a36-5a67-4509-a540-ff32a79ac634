"""
Tests for Cron Job Execution Logging

This module tests the cron logging functionality including:
- Log entry creation and management
- Automatic cleanup
- CLI commands
- Integration with cron worker
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

from erp.environment import EnvironmentManager
from erp.models import TransientModel
from addons.base.models.ir_cron_log import IrCronLog
from addons.base.models.ir_cron import IrCron


class TestIrCronLogModel:
    """Test the ir.cron.log model functionality"""

    @pytest.fixture
    async def env(self):
        """Create test environment"""
        return await EnvironmentManager.create_environment("test_db", 1)

    @pytest.fixture
    async def cron_job(self, env):
        """Create a test cron job"""
        cron_model = env['ir.cron']
        job_data = {
            'name': 'Test Job',
            'model': 'test.model',
            'function': 'test_method',
            'active': True,
            'logging_enabled': True,
            'user_id': 1,
            'priority': 5,
        }
        return await cron_model.create(job_data)

    @pytest.mark.asyncio
    async def test_log_model_attributes(self):
        """Test that the log model has correct attributes"""
        assert IrCronLog._name == 'ir.cron.log'
        assert IrCronLog._description == 'Cron Job Execution Logs'
        assert IrCronLog._transient is True
        assert IrCronLog._transient_max_hours == 24 * 7  # 7 days

    @pytest.mark.asyncio
    async def test_create_log_entry(self, env, cron_job):
        """Test creating a log entry"""
        log_model = env['ir.cron.log']
        
        log_entry = await log_model.create_log_entry(
            cron_job, "test_db", "worker_123"
        )
        
        assert log_entry is not None
        assert log_entry.cron_id == cron_job.id
        assert log_entry.job_name == cron_job.name
        assert log_entry.database_name == "test_db"
        assert log_entry.status == 'running'
        assert log_entry.model_name == cron_job.model
        assert log_entry.function_name == cron_job.function
        assert log_entry.worker_process_id == "worker_123"

    @pytest.mark.asyncio
    async def test_mark_success(self, env, cron_job):
        """Test marking a log entry as successful"""
        log_model = env['ir.cron.log']
        log_entry = await log_model.create_log_entry(cron_job, "test_db")
        
        result = "Job completed successfully"
        await log_entry.mark_success(result)
        
        assert log_entry.status == 'success'
        assert log_entry.result == result
        assert log_entry.end_time is not None
        assert log_entry.execution_time >= 0

    @pytest.mark.asyncio
    async def test_mark_failed(self, env, cron_job):
        """Test marking a log entry as failed"""
        log_model = env['ir.cron.log']
        log_entry = await log_model.create_log_entry(cron_job, "test_db")
        
        error_msg = "Test error message"
        traceback = "Traceback (most recent call last):\n  File..."
        await log_entry.mark_failed(error_msg, traceback)
        
        assert log_entry.status == 'failed'
        assert log_entry.error_message == error_msg
        assert log_entry.error_traceback == traceback
        assert log_entry.end_time is not None

    @pytest.mark.asyncio
    async def test_mark_timeout(self, env, cron_job):
        """Test marking a log entry as timed out"""
        log_model = env['ir.cron.log']
        log_entry = await log_model.create_log_entry(cron_job, "test_db")
        
        await log_entry.mark_timeout()
        
        assert log_entry.status == 'timeout'
        assert log_entry.error_message == 'Job execution timed out'
        assert log_entry.end_time is not None

    @pytest.mark.asyncio
    async def test_execution_time_calculation(self, env, cron_job):
        """Test execution time calculation"""
        log_model = env['ir.cron.log']
        log_entry = await log_model.create_log_entry(cron_job, "test_db")
        
        # Simulate some execution time
        await asyncio.sleep(0.1)
        await log_entry.mark_success("Done")
        
        assert log_entry.execution_time > 0
        assert log_entry.execution_time < 1  # Should be less than 1 second

    @pytest.mark.asyncio
    async def test_get_job_statistics(self, env, cron_job):
        """Test getting job execution statistics"""
        log_model = env['ir.cron.log']
        
        # Create multiple log entries with different statuses
        log1 = await log_model.create_log_entry(cron_job, "test_db")
        await log1.mark_success("Success 1")
        
        log2 = await log_model.create_log_entry(cron_job, "test_db")
        await log2.mark_success("Success 2")
        
        log3 = await log_model.create_log_entry(cron_job, "test_db")
        await log3.mark_failed("Error occurred")
        
        log4 = await log_model.create_log_entry(cron_job, "test_db")
        await log4.mark_timeout()
        
        # Get statistics
        stats = await log_model.get_job_statistics(cron_job.id, days=1)
        
        assert stats['total_executions'] == 4
        assert stats['successful'] == 2
        assert stats['failed'] == 1
        assert stats['timeouts'] == 1
        assert stats['success_rate'] == 50.0
        assert stats['average_execution_time'] >= 0


class TestCronJobLogging:
    """Test cron job logging integration"""

    @pytest.fixture
    async def env(self):
        """Create test environment"""
        return await EnvironmentManager.create_environment("test_db", 1)

    @pytest.mark.asyncio
    async def test_logging_enabled_field(self, env):
        """Test that cron jobs have logging_enabled field"""
        cron_model = env['ir.cron']
        job_data = {
            'name': 'Test Job',
            'model': 'test.model',
            'function': 'test_method',
            'active': True,
            'logging_enabled': True,
            'user_id': 1,
        }
        
        job = await cron_model.create(job_data)
        assert job.logging_enabled is True

    @pytest.mark.asyncio
    async def test_create_execution_log_enabled(self, env):
        """Test creating execution log when logging is enabled"""
        cron_model = env['ir.cron']
        job_data = {
            'name': 'Test Job',
            'model': 'test.model',
            'function': 'test_method',
            'active': True,
            'logging_enabled': True,
            'user_id': 1,
        }
        
        job = await cron_model.create(job_data)
        log_entry = await job.create_execution_log("test_db", "worker_123")
        
        assert log_entry is not None
        assert log_entry.cron_id == job.id

    @pytest.mark.asyncio
    async def test_create_execution_log_disabled(self, env):
        """Test that no log is created when logging is disabled"""
        cron_model = env['ir.cron']
        job_data = {
            'name': 'Test Job',
            'model': 'test.model',
            'function': 'test_method',
            'active': True,
            'logging_enabled': False,
            'user_id': 1,
        }
        
        job = await cron_model.create(job_data)
        log_entry = await job.create_execution_log("test_db", "worker_123")
        
        assert log_entry is None


class TestCronLogCleanup:
    """Test automatic cleanup of cron logs"""

    @pytest.fixture
    async def env(self):
        """Create test environment"""
        return await EnvironmentManager.create_environment("test_db", 1)

    @pytest.mark.asyncio
    async def test_cleanup_old_logs(self, env):
        """Test cleanup of old log entries"""
        log_model = env['ir.cron.log']
        
        # Create old log entry
        old_log_data = {
            'cron_id': 1,
            'job_name': 'Old Job',
            'database_name': 'test_db',
            'start_time': datetime.now() - timedelta(days=10),
            'status': 'success',
            'model_name': 'test.model',
            'function_name': 'test_method',
            'user_id': 1,
        }
        old_log = await log_model.create(old_log_data)
        
        # Create recent log entry
        recent_log_data = {
            'cron_id': 1,
            'job_name': 'Recent Job',
            'database_name': 'test_db',
            'start_time': datetime.now() - timedelta(hours=1),
            'status': 'success',
            'model_name': 'test.model',
            'function_name': 'test_method',
            'user_id': 1,
        }
        recent_log = await log_model.create(recent_log_data)
        
        # Run cleanup with 7 day retention
        await log_model.cleanup_old_logs(max_age_hours=7*24)
        
        # Check that old log was deleted and recent log remains
        old_exists = await log_model.browse(old_log.id).exists()
        recent_exists = await log_model.browse(recent_log.id).exists()
        
        assert not old_exists
        assert recent_exists

    @pytest.mark.asyncio
    async def test_transient_vacuum(self, env):
        """Test that transient vacuum works for cron logs"""
        log_model = env['ir.cron.log']
        
        # Create old log entry
        old_log_data = {
            'cron_id': 1,
            'job_name': 'Old Job',
            'database_name': 'test_db',
            'start_time': datetime.now() - timedelta(days=10),
            'status': 'success',
            'model_name': 'test.model',
            'function_name': 'test_method',
            'user_id': 1,
        }
        old_log = await log_model.create(old_log_data)
        
        # Run transient vacuum
        await log_model._transient_vacuum()
        
        # Check that old log was deleted
        old_exists = await log_model.browse(old_log.id).exists()
        assert not old_exists


class TestCronWorkerLogging:
    """Test cron worker logging integration"""

    @pytest.mark.asyncio
    async def test_worker_creates_log_entry(self):
        """Test that worker creates log entry when logging is enabled"""
        from erp.cron.worker import CronWorker
        
        # Mock job data with logging enabled
        job_data = {
            'id': 'test_job_123',
            'name': 'Test Job',
            'model': 'test.model',
            'function': 'test_method',
            'logging_enabled': True,
            'user_id': 1,
        }
        
        # Mock worker
        worker = CronWorker(1, ['test_db'], {}, Mock())
        
        with patch.object(worker, '_create_log_entry') as mock_create_log:
            mock_create_log.return_value = Mock()
            
            with patch.object(worker, '_run_job_method') as mock_run_job:
                mock_run_job.return_value = "Success"
                
                await worker._execute_job('test_db', job_data)
                
                # Verify log entry was created
                mock_create_log.assert_called_once_with('test_db', job_data)

    @pytest.mark.asyncio
    async def test_worker_skips_log_when_disabled(self):
        """Test that worker skips logging when disabled"""
        from erp.cron.worker import CronWorker
        
        # Mock job data with logging disabled
        job_data = {
            'id': 'test_job_123',
            'name': 'Test Job',
            'model': 'test.model',
            'function': 'test_method',
            'logging_enabled': False,
            'user_id': 1,
        }
        
        # Mock worker
        worker = CronWorker(1, ['test_db'], {}, Mock())
        
        with patch.object(worker, '_create_log_entry') as mock_create_log:
            with patch.object(worker, '_run_job_method') as mock_run_job:
                mock_run_job.return_value = "Success"
                
                await worker._execute_job('test_db', job_data)
                
                # Verify log entry was not created
                mock_create_log.assert_not_called()


class TestCronLogsCLI:
    """Test cron logs CLI commands"""

    @pytest.mark.asyncio
    async def test_cron_logs_command_basic(self):
        """Test basic cron logs command"""
        from erp.cli.cron import CronLogsCommand

        command = CronLogsCommand()

        # Mock arguments
        args = Mock()
        args.database = "test_db"
        args.job_id = None
        args.job_name = None
        args.status = None
        args.days = 7
        args.limit = 50
        args.format = "table"

        # Mock environment and logs
        with patch('erp.environment.EnvironmentManager.create_environment') as mock_env:
            mock_log_model = Mock()
            mock_log_model.search = AsyncMock(return_value=[])

            mock_environment = Mock()
            mock_environment.__getitem__.return_value = mock_log_model
            mock_env.return_value = mock_environment

            result = await command.run(args)

            assert result == 0
            mock_log_model.search.assert_called_once()

    @pytest.mark.asyncio
    async def test_cron_logs_command_with_filters(self):
        """Test cron logs command with filters"""
        from erp.cli.cron import CronLogsCommand

        command = CronLogsCommand()

        # Mock arguments with filters
        args = Mock()
        args.database = "test_db"
        args.job_id = "123"
        args.job_name = "Test Job"
        args.status = "failed"
        args.days = 3
        args.limit = 25
        args.format = "json"

        # Mock environment and logs
        with patch('erp.environment.EnvironmentManager.create_environment') as mock_env:
            mock_log_model = Mock()
            mock_log_model.search = AsyncMock(return_value=[])

            mock_environment = Mock()
            mock_environment.__getitem__.return_value = mock_log_model
            mock_env.return_value = mock_environment

            result = await command.run(args)

            assert result == 0

            # Verify search was called with correct domain
            call_args = mock_log_model.search.call_args
            domain = call_args[0][0]

            # Check that filters were applied
            assert ('cron_id', '=', '123') in domain
            assert any('job_name' in filter_tuple for filter_tuple in domain)
            assert ('status', '=', 'failed') in domain

    @pytest.mark.asyncio
    async def test_cron_logs_command_error_handling(self):
        """Test cron logs command error handling"""
        from erp.cli.cron import CronLogsCommand

        command = CronLogsCommand()

        # Mock arguments without database
        args = Mock()
        args.database = None

        result = await command.run(args)
        assert result == 1

    @pytest.mark.asyncio
    async def test_display_logs_table_format(self):
        """Test displaying logs in table format"""
        from erp.cli.cron import CronLogsCommand

        command = CronLogsCommand()

        # Mock log entries
        mock_log1 = Mock()
        mock_log1.id = "1"
        mock_log1.job_name = "Test Job 1"
        mock_log1.status = "success"
        mock_log1.start_time = datetime.now()
        mock_log1.execution_time = 1.23

        mock_log2 = Mock()
        mock_log2.id = "2"
        mock_log2.job_name = "Test Job 2"
        mock_log2.status = "failed"
        mock_log2.start_time = datetime.now()
        mock_log2.execution_time = 0.45

        logs = [mock_log1, mock_log2]

        # Capture output
        with patch.object(command, 'print_info') as mock_print:
            await command._display_logs_table(logs)

            # Verify that output was printed
            assert mock_print.call_count > 0

            # Check that header and data rows were printed
            calls = [call[0][0] for call in mock_print.call_args_list]
            assert any("Found 2 log entries" in call for call in calls)
            assert any("ID" in call and "Job Name" in call for call in calls)

    @pytest.mark.asyncio
    async def test_display_logs_json_format(self):
        """Test displaying logs in JSON format"""
        from erp.cli.cron import CronLogsCommand

        command = CronLogsCommand()

        # Mock log entry
        mock_log = Mock()
        mock_log.id = "1"
        mock_log.cron_id = "123"
        mock_log.job_name = "Test Job"
        mock_log.database_name = "test_db"
        mock_log.status = "success"
        mock_log.model_name = "test.model"
        mock_log.function_name = "test_method"
        mock_log.start_time = datetime.now()
        mock_log.end_time = datetime.now()
        mock_log.execution_time = 1.23
        mock_log.arguments = "[]"
        mock_log.result = "Success"
        mock_log.error_message = None
        mock_log.worker_process_id = "worker_1"

        logs = [mock_log]

        # Capture JSON output
        with patch('builtins.print') as mock_print:
            await command._display_logs_json(logs)

            # Verify JSON was printed
            mock_print.assert_called_once()
            output = mock_print.call_args[0][0]

            # Verify it's valid JSON containing expected data
            import json
            data = json.loads(output)
            assert len(data) == 1
            assert data[0]['id'] == "1"
            assert data[0]['job_name'] == "Test Job"
            assert data[0]['status'] == "success"
