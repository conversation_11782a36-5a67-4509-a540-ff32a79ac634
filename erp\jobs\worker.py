"""
Job Worker Process - Individual worker that executes jobs from queues
"""

import asyncio
import multiprocessing
import signal
import sys
import time
from datetime import datetime
from typing import Dict, List, Optional

from ..logging import get_logger
from .executor import JobExecutor


class JobWorker:
    """
    Individual job worker process that executes jobs from queues.
    
    This worker:
    1. Runs in a separate process from the main server
    2. Manages multiple databases and channels
    3. Processes jobs from assigned channels
    4. Handles job failures and retries
    5. Responds to shutdown signals
    6. Integrates with existing cron worker infrastructure
    """

    def __init__(self, process_id: int, databases: List[str], channels: List[str], 
                 config: Dict, shutdown_event: multiprocessing.Event):
        self.process_id = process_id
        self.databases = databases
        self.channels = channels
        self.config = config
        self.shutdown_event = shutdown_event
        
        self.logger = get_logger(f"{__name__}.{process_id}")
        self.is_running = False
        self.start_time = None
        
        # Job execution tracking
        self.jobs_executed = 0
        self.jobs_failed = 0
        self.total_execution_time = 0.0
        self.last_job_check = {}
        
        # Initialize job executor
        self.executor = JobExecutor(config)
        
        # Performance tracking
        self.performance_metrics = {
            'jobs_per_minute': 0.0,
            'average_execution_time': 0.0,
            'error_rate': 0.0
        }

    def run(self):
        """Main entry point for the worker process."""
        try:
            # Set up signal handlers
            signal.signal(signal.SIGTERM, self._signal_handler)
            signal.signal(signal.SIGINT, self._signal_handler)
            
            # Run the async main loop
            asyncio.run(self._async_main())
            
        except Exception as e:
            self.logger.error(f"Worker {self.process_id} crashed: {e}")
            sys.exit(1)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"Worker {self.process_id} received signal {signum}, shutting down...")
        self.shutdown_event.set()

    async def _async_main(self):
        """Async main loop for the worker."""
        try:
            self.logger.info(f"Starting job worker {self.process_id} for databases: {self.databases}, channels: {self.channels}")
            self.is_running = True
            self.start_time = datetime.now()
            
            # Initialize databases
            await self._initialize_databases()
            
            # Start main processing loop
            await self._main_loop()
            
        except Exception as e:
            self.logger.error(f"Error in worker {self.process_id} main loop: {e}")
        finally:
            self.is_running = False
            self.logger.info(f"Worker {self.process_id} stopped")

    async def _initialize_databases(self):
        """Initialize database connections and registries."""
        for db_name in self.databases:
            try:
                registry = await self._get_registry(db_name)
                if registry:
                    self.logger.debug(f"Initialized registry for database {db_name}")
                else:
                    self.logger.warning(f"Failed to initialize registry for database {db_name}")
            except Exception as e:
                self.logger.error(f"Error initializing database {db_name}: {e}")

    async def _main_loop(self):
        """Main worker loop that checks and executes jobs."""
        check_interval = self.config.get('job_check_interval', 30)  # Check every 30 seconds
        
        self.logger.info(f"Worker {self.process_id} starting main loop (check interval: {check_interval}s)")
        
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Check for jobs in each database/channel combination
                for db_name in self.databases:
                    if self.shutdown_event.is_set():
                        break
                    
                    await self._check_database_jobs(db_name)
                
                # Update performance metrics
                self._update_performance_metrics()
                
                # Wait for next check or shutdown signal
                for _ in range(check_interval):
                    if self.shutdown_event.is_set():
                        break
                    await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in worker {self.process_id} main loop: {e}")
                await asyncio.sleep(5)  # Brief pause before retrying
        
        self.logger.info(f"Worker {self.process_id} main loop ended")

    async def _check_database_jobs(self, db_name: str):
        """Check for jobs to execute in a specific database."""
        try:
            # Initialize registry for this database
            registry = await self._get_registry(db_name)
            if not registry:
                return
            
            # Get jobs that are ready to run for our channels
            jobs = await self._get_jobs_to_run(db_name)
            
            if jobs:
                self.logger.debug(f"Found {len(jobs)} jobs to run in database {db_name}")
                
                for job in jobs:
                    if self.shutdown_event.is_set():
                        break
                    
                    await self._execute_job(db_name, job)
            
            self.last_job_check[db_name] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error checking jobs for database {db_name}: {e}")

    async def _get_jobs_to_run(self, db_name: str) -> List[Dict]:
        """Get jobs that are ready to run for this worker's channels."""
        try:
            from ..environment import EnvironmentManager
            
            # Create environment
            env = await EnvironmentManager.create_environment(db_name, 1)  # Use admin user
            job_model = env['ir.job']
            
            # Get pending jobs for our channels
            domain = [
                ('state', '=', 'pending'),
                ('scheduled_at', '<=', datetime.now()),
                ('database_name', '=', db_name)
            ]
            
            # Filter by channels if specified
            if self.channels:
                channel_model = env['ir.job.channel']
                channel_ids = []
                for channel_name in self.channels:
                    channel = channel_model.search([('name', '=', channel_name)], limit=1)
                    if channel:
                        channel_ids.append(channel.id)
                
                if channel_ids:
                    domain.append(('channel_id', 'in', channel_ids))
            
            # Get jobs ordered by priority and scheduled time
            jobs = job_model.search(domain, order='priority ASC, scheduled_at ASC', limit=10)
            
            # Convert to dictionaries for processing
            job_data = []
            for job in jobs:
                job_dict = {
                    'id': job.id,
                    'uuid': job.uuid,
                    'name': job.name,
                    'model': job.model,
                    'function': job.function,
                    'args': job.args,
                    'kwargs': job.kwargs,
                    'user_id': job.user_id.id if job.user_id else 1,
                    'priority': job.priority,
                    'max_retries': job.max_retries,
                    'retry_count': job.retry_count,
                    'logging_enabled': job.logging_enabled,
                    'channel_id': job.channel_id.id if job.channel_id else None,
                }
                job_data.append(job_dict)
            
            return job_data
            
        except Exception as e:
            self.logger.error(f"Failed to get jobs for database {db_name}: {e}")
            return []

    async def _execute_job(self, db_name: str, job: Dict):
        """Execute a single job."""
        job_name = job.get('name', 'Unknown Job')
        job_id = job.get('id', 'Unknown ID')
        job_uuid = job.get('uuid', 'Unknown UUID')
        
        start_time = time.time()
        
        try:
            self.logger.info(f"Executing job {job_name} ({job_uuid}) in database {db_name}")

            # Mark job as running
            await self._update_job_status(db_name, job_id, 'running', worker_pid=self.process_id)

            # Create log entry if logging is enabled
            log_entry = None
            if job.get('logging_enabled', True):
                log_entry = await self._create_log_entry(db_name, job)

            # Execute the job with retry logic
            success, error_msg, result = await self.executor.execute_job_with_retry(db_name, job)

            # Update statistics
            execution_time = time.time() - start_time
            self.jobs_executed += 1
            self.total_execution_time += execution_time

            if success:
                # Mark job as completed
                await self._update_job_status(db_name, job_id, 'done', result=result)
                
                # Update log entry
                if log_entry:
                    await self._update_log_entry(log_entry, 'completed', result=result)
                
                self.logger.info(f"Job {job_name} ({job_uuid}) completed successfully in {execution_time:.2f}s")
            else:
                # Mark job as failed
                await self._update_job_status(db_name, job_id, 'failed', error_message=error_msg)
                
                # Update log entry
                if log_entry:
                    await self._update_log_entry(log_entry, 'failed', error_message=error_msg)
                
                self.jobs_failed += 1
                self.logger.error(f"Job {job_name} ({job_uuid}) failed: {error_msg}")

        except Exception as e:
            execution_time = time.time() - start_time
            self.jobs_failed += 1
            self.total_execution_time += execution_time
            
            error_msg = f"Unexpected error executing job: {str(e)}"
            self.logger.error(f"Job {job_name} ({job_uuid}) crashed: {error_msg}")
            
            # Mark job as failed
            try:
                await self._update_job_status(db_name, job_id, 'failed', error_message=error_msg)
            except Exception as update_error:
                self.logger.error(f"Failed to update job status: {update_error}")

    async def _get_registry(self, db_name: str):
        """Get or initialize registry for a database."""
        try:
            from ..database.memory.registry_manager import MemoryRegistryManager
            
            # Initialize registry with delay for job processing
            registry = await MemoryRegistryManager.initialize_registry_with_delay(
                db_name, delay_seconds=1.0
            )
            
            return registry
            
        except Exception as e:
            self.logger.error(f"Failed to get registry for database {db_name}: {e}")
            return None

    async def _update_job_status(self, db_name: str, job_id: str, state: str, 
                                worker_pid: Optional[int] = None, 
                                result: any = None, 
                                error_message: Optional[str] = None):
        """Update job status in the database."""
        try:
            from ..environment import EnvironmentManager
            
            env = await EnvironmentManager.create_environment(db_name, 1)
            job_model = env['ir.job']
            job = job_model.browse(job_id)
            
            if state == 'running':
                job.mark_running(worker_pid)
            elif state == 'done':
                job.mark_done(result)
            elif state == 'failed':
                job.mark_failed(error_message)
            
        except Exception as e:
            self.logger.error(f"Failed to update job status: {e}")

    async def _create_log_entry(self, db_name: str, job: Dict):
        """Create a log entry for job execution."""
        try:
            from ..environment import EnvironmentManager
            
            env = await EnvironmentManager.create_environment(db_name, 1)
            job_model = env['ir.job']
            job_record = job_model.browse(job['id'])
            
            return await job_record.create_execution_log(str(self.process_id))
            
        except Exception as e:
            self.logger.error(f"Failed to create log entry: {e}")
            return None

    async def _update_log_entry(self, log_entry, state: str, result: any = None, error_message: str = None):
        """Update a log entry with execution results."""
        try:
            if state == 'completed':
                log_entry.mark_completed(result)
            elif state == 'failed':
                log_entry.mark_failed(error_message)
            
        except Exception as e:
            self.logger.error(f"Failed to update log entry: {e}")

    def _update_performance_metrics(self):
        """Update performance metrics."""
        if self.start_time:
            runtime_minutes = (datetime.now() - self.start_time).total_seconds() / 60
            if runtime_minutes > 0:
                self.performance_metrics['jobs_per_minute'] = self.jobs_executed / runtime_minutes
        
        if self.jobs_executed > 0:
            self.performance_metrics['average_execution_time'] = self.total_execution_time / self.jobs_executed
            self.performance_metrics['error_rate'] = self.jobs_failed / self.jobs_executed
        
    def get_status(self) -> Dict:
        """Get worker status information."""
        return {
            'process_id': self.process_id,
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'databases': self.databases,
            'channels': self.channels,
            'jobs_executed': self.jobs_executed,
            'jobs_failed': self.jobs_failed,
            'total_execution_time': self.total_execution_time,
            'performance_metrics': self.performance_metrics,
            'last_job_check': {db: check_time.isoformat() for db, check_time in self.last_job_check.items()}
        }
