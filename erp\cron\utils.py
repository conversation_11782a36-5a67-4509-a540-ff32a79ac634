"""
Cron Utilities - Helper functions for cron expression parsing and scheduling

This module provides utilities for:
- Parsing cron expressions
- Calculating next execution times
- Validating cron syntax
- Converting between different time formats
"""

import re
from datetime import datetime, timedelta
from typing import Optional, Tuple, List


class CronUtils:
    """Utility class for cron-related operations."""

    # Cron field ranges
    FIELD_RANGES = {
        'minute': (0, 59),
        'hour': (0, 23),
        'day': (1, 31),
        'month': (1, 12),
        'weekday': (0, 6),  # 0 = Sunday
    }

    # Month names mapping
    MONTH_NAMES = {
        'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
        'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
    }

    # Weekday names mapping
    WEEKDAY_NAMES = {
        'sun': 0, 'mon': 1, 'tue': 2, 'wed': 3, 'thu': 4, 'fri': 5, 'sat': 6
    }

    @classmethod
    def parse_cron_expression(cls, cron_expr: str) -> Optional[Tuple[List, List, List, List, List]]:
        """
        Parse a cron expression into its components.
        
        Args:
            cron_expr: Cron expression (e.g., "0 */6 * * *")
            
        Returns:
            Tuple of (minutes, hours, days, months, weekdays) lists or None if invalid
        """
        if not cron_expr or not isinstance(cron_expr, str):
            return None
        
        # Split the expression
        parts = cron_expr.strip().split()
        
        if len(parts) != 5:
            return None
        
        try:
            minutes = cls._parse_field(parts[0], 'minute')
            hours = cls._parse_field(parts[1], 'hour')
            days = cls._parse_field(parts[2], 'day')
            months = cls._parse_field(parts[3], 'month')
            weekdays = cls._parse_field(parts[4], 'weekday')
            
            return (minutes, hours, days, months, weekdays)
            
        except ValueError:
            return None

    @classmethod
    def _parse_field(cls, field: str, field_type: str) -> List[int]:
        """Parse a single cron field."""
        min_val, max_val = cls.FIELD_RANGES[field_type]
        
        # Handle special characters
        if field == '*':
            return list(range(min_val, max_val + 1))
        
        # Handle step values (e.g., */5, 1-10/2)
        if '/' in field:
            range_part, step_part = field.split('/', 1)
            step = int(step_part)
            
            if range_part == '*':
                base_range = list(range(min_val, max_val + 1))
            else:
                base_range = cls._parse_range(range_part, field_type)
            
            return [val for val in base_range if (val - min_val) % step == 0]
        
        # Handle comma-separated values
        if ',' in field:
            values = []
            for part in field.split(','):
                values.extend(cls._parse_range(part.strip(), field_type))
            return sorted(list(set(values)))
        
        # Handle single range or value
        return cls._parse_range(field, field_type)

    @classmethod
    def _parse_range(cls, range_str: str, field_type: str) -> List[int]:
        """Parse a range string (e.g., '1-5', '10', 'mon-fri')."""
        min_val, max_val = cls.FIELD_RANGES[field_type]
        
        # Handle named values (months/weekdays)
        range_str = cls._convert_names(range_str, field_type)
        
        if '-' in range_str:
            # Range (e.g., 1-5)
            start_str, end_str = range_str.split('-', 1)
            start = int(start_str)
            end = int(end_str)
            
            if start < min_val or end > max_val or start > end:
                raise ValueError(f"Invalid range: {range_str}")
            
            return list(range(start, end + 1))
        else:
            # Single value
            value = int(range_str)
            if value < min_val or value > max_val:
                raise ValueError(f"Value {value} out of range for {field_type}")
            return [value]

    @classmethod
    def _convert_names(cls, value: str, field_type: str) -> str:
        """Convert named values to numbers."""
        value_lower = value.lower()
        
        if field_type == 'month':
            for name, num in cls.MONTH_NAMES.items():
                value_lower = value_lower.replace(name, str(num))
        elif field_type == 'weekday':
            for name, num in cls.WEEKDAY_NAMES.items():
                value_lower = value_lower.replace(name, str(num))
        
        return value_lower

    @classmethod
    def validate_cron_expression(cls, cron_expr: str) -> Tuple[bool, str]:
        """
        Validate a cron expression.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not cron_expr or not isinstance(cron_expr, str):
            return False, "Cron expression cannot be empty"
        
        parts = cron_expr.strip().split()
        if len(parts) != 5:
            return False, "Cron expression must have exactly 5 fields (minute hour day month weekday)"
        
        field_names = ['minute', 'hour', 'day', 'month', 'weekday']
        
        for i, (part, field_name) in enumerate(zip(parts, field_names)):
            try:
                cls._parse_field(part, field_name)
            except ValueError as e:
                return False, f"Invalid {field_name} field '{part}': {e}"
        
        return True, ""

    @classmethod
    def calculate_next_run(cls, cron_expr: str, from_time: datetime = None) -> Optional[datetime]:
        """
        Calculate the next execution time for a cron expression.
        
        Args:
            cron_expr: Cron expression
            from_time: Calculate from this time (default: now)
            
        Returns:
            Next execution datetime or None if invalid expression
        """
        if from_time is None:
            from_time = datetime.now()
        
        parsed = cls.parse_cron_expression(cron_expr)
        if not parsed:
            return None
        
        minutes, hours, days, months, weekdays = parsed
        
        # Start from the next minute
        next_time = from_time.replace(second=0, microsecond=0) + timedelta(minutes=1)
        
        # Find the next valid time (with a reasonable limit to prevent infinite loops)
        max_iterations = 366 * 24 * 60  # One year worth of minutes
        iterations = 0
        
        while iterations < max_iterations:
            if (next_time.minute in minutes and
                next_time.hour in hours and
                next_time.day in days and
                next_time.month in months and
                next_time.weekday() + 1 in weekdays):  # Convert to Sunday=0 format
                return next_time
            
            next_time += timedelta(minutes=1)
            iterations += 1
        
        return None  # Could not find a valid time

    @classmethod
    def calculate_interval_next_run(cls, interval_number: int, interval_type: str, 
                                  last_run: datetime = None) -> datetime:
        """
        Calculate next run time based on interval settings.
        
        Args:
            interval_number: Number of intervals
            interval_type: Type of interval (minutes, hours, days, weeks, months)
            last_run: Last execution time (default: now)
            
        Returns:
            Next execution datetime
        """
        if last_run is None:
            last_run = datetime.now()
        
        if interval_type == 'minutes':
            delta = timedelta(minutes=interval_number)
        elif interval_type == 'hours':
            delta = timedelta(hours=interval_number)
        elif interval_type == 'days':
            delta = timedelta(days=interval_number)
        elif interval_type == 'weeks':
            delta = timedelta(weeks=interval_number)
        elif interval_type == 'months':
            # Approximate months as 30 days
            delta = timedelta(days=interval_number * 30)
        else:
            # Default to hours
            delta = timedelta(hours=interval_number)
        
        return last_run + delta

    @classmethod
    def get_cron_description(cls, cron_expr: str) -> str:
        """
        Get a human-readable description of a cron expression.
        
        Args:
            cron_expr: Cron expression
            
        Returns:
            Human-readable description
        """
        parsed = cls.parse_cron_expression(cron_expr)
        if not parsed:
            return "Invalid cron expression"
        
        minutes, hours, days, months, weekdays = parsed
        
        # Build description parts
        parts = []
        
        # Time part
        if len(minutes) == 1 and len(hours) == 1:
            parts.append(f"at {hours[0]:02d}:{minutes[0]:02d}")
        elif len(minutes) == 1:
            if minutes[0] == 0:
                parts.append(f"at the top of hours {cls._format_list(hours)}")
            else:
                parts.append(f"at minute {minutes[0]} of hours {cls._format_list(hours)}")
        elif len(hours) == 1:
            parts.append(f"at hour {hours[0]} on minutes {cls._format_list(minutes)}")
        else:
            parts.append(f"at minutes {cls._format_list(minutes)} of hours {cls._format_list(hours)}")
        
        # Day/weekday part
        if len(days) < 31 and len(weekdays) < 7:
            parts.append(f"on days {cls._format_list(days)} and weekdays {cls._format_weekdays(weekdays)}")
        elif len(days) < 31:
            parts.append(f"on days {cls._format_list(days)} of the month")
        elif len(weekdays) < 7:
            parts.append(f"on {cls._format_weekdays(weekdays)}")
        
        # Month part
        if len(months) < 12:
            parts.append(f"in {cls._format_months(months)}")
        
        return " ".join(parts)

    @classmethod
    def _format_list(cls, values: List[int]) -> str:
        """Format a list of integers for display."""
        if len(values) == 1:
            return str(values[0])
        elif len(values) <= 3:
            return ", ".join(map(str, values))
        else:
            return f"{values[0]}-{values[-1]} (every {values[1] - values[0]})" if len(set(values[1:]) - set(values[:-1])) == 1 else f"{len(values)} values"

    @classmethod
    def _format_weekdays(cls, weekdays: List[int]) -> str:
        """Format weekday numbers as names."""
        weekday_names = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
        names = [weekday_names[wd] for wd in weekdays if 0 <= wd <= 6]
        
        if len(names) == 1:
            return names[0]
        elif len(names) <= 3:
            return ", ".join(names)
        else:
            return f"{len(names)} weekdays"

    @classmethod
    def _format_months(cls, months: List[int]) -> str:
        """Format month numbers as names."""
        month_names = ['', 'January', 'February', 'March', 'April', 'May', 'June',
                      'July', 'August', 'September', 'October', 'November', 'December']
        names = [month_names[m] for m in months if 1 <= m <= 12]
        
        if len(names) == 1:
            return names[0]
        elif len(names) <= 3:
            return ", ".join(names)
        else:
            return f"{len(names)} months"

    @classmethod
    def is_time_to_run(cls, cron_expr: str, check_time: datetime = None) -> bool:
        """
        Check if it's time to run a cron job.
        
        Args:
            cron_expr: Cron expression
            check_time: Time to check (default: now)
            
        Returns:
            True if it's time to run the job
        """
        if check_time is None:
            check_time = datetime.now()
        
        parsed = cls.parse_cron_expression(cron_expr)
        if not parsed:
            return False
        
        minutes, hours, days, months, weekdays = parsed
        
        return (check_time.minute in minutes and
                check_time.hour in hours and
                check_time.day in days and
                check_time.month in months and
                (check_time.weekday() + 1) % 7 in weekdays)  # Convert to Sunday=0 format
