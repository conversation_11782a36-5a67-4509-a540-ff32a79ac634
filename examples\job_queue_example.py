"""
Example demonstrating the job queue functionality
"""

import asyncio
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def job_queue_example():
    """Demonstrate job queue functionality"""
    
    try:
        # Import required modules
        from erp.environment import EnvironmentManager
        from erp.models import Model
        from erp import fields
        
        logger.info("=== Job Queue Example ===")
        
        # Create environment (assuming 'test_db' database exists)
        env = await EnvironmentManager.create_environment('test_db', 1)
        
        # Example 1: Create a simple model with job queue capabilities
        class ExampleModel(Model):
            _name = 'example.model'
            _description = 'Example Model for Job Queue Demo'
            
            name = fields.Char(string='Name', required=True)
            description = fields.Text(string='Description')
            
            async def process_data(self, data_type='default', batch_size=100):
                """Example method that can be queued as a job"""
                logger.info(f"Processing {data_type} data with batch size {batch_size}")
                
                # Simulate some work
                await asyncio.sleep(2)
                
                result = {
                    'processed_items': batch_size,
                    'data_type': data_type,
                    'timestamp': datetime.now().isoformat(),
                    'status': 'completed'
                }
                
                logger.info(f"Data processing completed: {result}")
                return result
            
            async def send_notification(self, recipient, message):
                """Example notification method"""
                logger.info(f"Sending notification to {recipient}: {message}")
                
                # Simulate email sending
                await asyncio.sleep(1)
                
                return f"Notification sent to {recipient}"
            
            @classmethod
            def generate_report(cls, report_type='summary'):
                """Example class method for report generation"""
                logger.info(f"Generating {report_type} report")
                
                # Simulate report generation
                import time
                time.sleep(3)
                
                return {
                    'report_type': report_type,
                    'generated_at': datetime.now().isoformat(),
                    'file_path': f'/tmp/{report_type}_report.pdf'
                }
        
        # Create an instance of the example model
        example_model = env['example.model']
        
        # Example 2: Queue immediate jobs
        logger.info("\n--- Queuing Immediate Jobs ---")
        
        # Queue a simple job with default priority
        job1 = await example_model.queue().process_data('customer_data', 50)
        logger.info(f"Queued job 1: {job1.uuid} - {job1.name}")
        
        # Queue a high priority job
        job2 = await example_model.queue(
            priority=1, 
            channel='high_priority'
        ).send_notification('<EMAIL>', 'System maintenance completed')
        logger.info(f"Queued job 2: {job2.uuid} - {job2.name}")
        
        # Example 3: Queue delayed jobs
        logger.info("\n--- Queuing Delayed Jobs ---")
        
        # Queue a job to run in 5 minutes
        job3 = await example_model.queue(
            delay=300,  # 5 minutes
            channel='background',
            name='Delayed Data Processing'
        ).process_data('analytics_data', 200)
        logger.info(f"Queued delayed job: {job3.uuid} - {job3.name}")
        
        # Example 4: Queue class method
        logger.info("\n--- Queuing Class Method ---")
        
        job4 = await ExampleModel.queue_class_method(
            'generate_report',
            env,
            name='Monthly Report Generation',
            priority=2,
            channel='reports',
            'monthly'  # argument for the method
        )
        logger.info(f"Queued class method job: {job4.uuid} - {job4.name}")
        
        # Example 5: Queue job with custom configuration
        logger.info("\n--- Queuing Job with Custom Configuration ---")
        
        job5 = await example_model.queue(
            name='Critical Data Sync',
            priority=0,  # Highest priority
            channel='high_priority',
            max_retries=5,
            logging_enabled=True
        ).process_data('critical_sync', 1000)
        logger.info(f"Queued critical job: {job5.uuid} - {job5.name}")
        
        # Example 6: Check job status and manage jobs
        logger.info("\n--- Job Management ---")
        
        # Get all queued jobs for this model
        queued_jobs = await example_model.get_queued_jobs(['pending', 'running'])
        logger.info(f"Found {len(queued_jobs)} queued jobs")
        
        for job in queued_jobs[:3]:  # Show first 3 jobs
            logger.info(f"  - {job.name} (Priority: {job.priority}, State: {job.state})")
        
        # Example 7: Working with job channels
        logger.info("\n--- Job Channels ---")
        
        channel_model = env['ir.job.channel']
        channels = channel_model.search([('active', '=', True)])
        
        logger.info(f"Available job channels:")
        for channel in channels:
            logger.info(f"  - {channel.name}: {channel.description} (Capacity: {channel.capacity})")
        
        # Example 8: Job logging
        logger.info("\n--- Job Logging ---")
        
        log_model = env['ir.job.log']
        recent_logs = log_model.search([], order='started_at DESC', limit=5)
        
        logger.info(f"Recent job executions:")
        for log in recent_logs:
            logger.info(f"  - {log.job_name}: {log.state} ({log.duration:.2f}s)")
        
        logger.info("\n=== Job Queue Example Completed ===")
        logger.info("Jobs have been queued and will be processed by job workers.")
        logger.info("Check the job logs to see execution results.")
        
    except Exception as e:
        logger.error(f"Error in job queue example: {e}")
        raise


async def monitor_job_execution():
    """Monitor job execution for a few minutes"""
    
    try:
        from erp.environment import EnvironmentManager
        
        logger.info("\n=== Monitoring Job Execution ===")
        
        env = await EnvironmentManager.create_environment('test_db', 1)
        job_model = env['ir.job']
        
        # Monitor for 2 minutes
        for i in range(24):  # 24 * 5 seconds = 2 minutes
            pending_jobs = job_model.search([('state', '=', 'pending')])
            running_jobs = job_model.search([('state', '=', 'running')])
            completed_jobs = job_model.search([('state', '=', 'done')])
            failed_jobs = job_model.search([('state', '=', 'failed')])
            
            logger.info(f"Jobs - Pending: {len(pending_jobs)}, Running: {len(running_jobs)}, "
                       f"Completed: {len(completed_jobs)}, Failed: {len(failed_jobs)}")
            
            if len(pending_jobs) == 0 and len(running_jobs) == 0:
                logger.info("All jobs completed!")
                break
            
            await asyncio.sleep(5)
        
        logger.info("=== Monitoring Completed ===")
        
    except Exception as e:
        logger.error(f"Error monitoring jobs: {e}")


if __name__ == "__main__":
    async def main():
        # Run the job queue example
        await job_queue_example()
        
        # Monitor job execution
        await monitor_job_execution()
    
    # Run the example
    asyncio.run(main())
