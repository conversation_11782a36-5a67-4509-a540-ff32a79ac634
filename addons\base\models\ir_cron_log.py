"""
Cron Job Execution Logging Model

This module provides the ir.cron.log model for tracking cron job executions.
The model is transient and automatically cleans up old log entries.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional

from erp.models import TransientModel, fields
from erp.logging import get_logger


class IrCronLog(TransientModel):
    """Model for logging cron job executions"""

    _name = 'ir.cron.log'
    _description = 'Cron Job Execution Logs'
    _order = 'start_time desc'
    
    # Configure transient behavior - keep logs for 7 days by default
    _transient_max_hours = 24 * 7  # 7 days
    
    # Define SQL constraints for data integrity
    _sql_constraints = [
        ('check_execution_time_positive',
         'CHECK (execution_time >= 0)',
         'Execution time must be non-negative'),
        ('check_start_before_end',
         'CHECK (end_time IS NULL OR end_time >= start_time)',
         'End time must be after start time'),
    ]

    # Job identification
    cron_id = fields.Many2One(
        'ir.cron', string='Cron Job', required=True, index=True,
        help='The cron job that was executed'
    )
    
    job_name = fields.Char(
        string='Job Name', required=True, index=True,
        help='Name of the cron job at execution time'
    )
    
    database_name = fields.Char(
        string='Database', required=True, index=True,
        help='Database where the job was executed'
    )
    
    # Execution details
    start_time = fields.Datetime(
        string='Start Time', required=True, index=True,
        help='When the job execution started'
    )
    
    end_time = fields.Datetime(
        string='End Time', index=True,
        help='When the job execution completed'
    )
    
    execution_time = fields.Float(
        string='Execution Time (seconds)', readonly=True,
        help='Total execution time in seconds'
    )
    
    # Status and results
    status = fields.Selection([
        ('running', 'Running'),
        ('success', 'Success'),
        ('failed', 'Failed'),
        ('timeout', 'Timeout'),
        ('cancelled', 'Cancelled'),
    ], string='Status', required=True, default='running', index=True,
        help='Execution status of the job')
    
    result = fields.Text(
        string='Result',
        help='Result returned by the job method'
    )
    
    error_message = fields.Text(
        string='Error Message',
        help='Error message if the job failed'
    )
    
    error_traceback = fields.Text(
        string='Error Traceback',
        help='Full error traceback if the job failed'
    )
    
    # Job configuration at execution time
    model_name = fields.Char(
        string='Model', required=True,
        help='Target model name'
    )
    
    function_name = fields.Char(
        string='Function', required=True,
        help='Method name that was called'
    )
    
    arguments = fields.Text(
        string='Arguments',
        help='Arguments passed to the method'
    )
    
    user_id = fields.Many2One(
        'res.users', string='User', required=True,
        help='User context for job execution'
    )
    
    priority = fields.Integer(
        string='Priority',
        help='Job priority at execution time'
    )
    
    # Worker information
    worker_process_id = fields.Char(
        string='Worker Process ID',
        help='ID of the worker process that executed the job'
    )
    
    # Additional metadata
    retry_count = fields.Integer(
        string='Retry Count', default=0,
        help='Number of times this execution was retried'
    )
    
    notes = fields.Text(
        string='Notes',
        help='Additional notes about the execution'
    )

    def _compute_execution_time(self):
        """Compute execution time when end_time is set"""
        for record in self:
            if record.start_time and record.end_time:
                start = record.start_time
                end = record.end_time
                if isinstance(start, str):
                    start = datetime.fromisoformat(start.replace('Z', '+00:00'))
                if isinstance(end, str):
                    end = datetime.fromisoformat(end.replace('Z', '+00:00'))
                
                delta = end - start
                record.execution_time = delta.total_seconds()
            else:
                record.execution_time = 0.0

    @classmethod
    async def create_log_entry(cls, cron_job, database_name: str, worker_process_id: str = None) -> 'IrCronLog':
        """Create a new log entry for a cron job execution"""
        log_data = {
            'cron_id': cron_job.id,
            'job_name': cron_job.name,
            'database_name': database_name,
            'start_time': datetime.now(),
            'status': 'running',
            'model_name': cron_job.model,
            'function_name': cron_job.function,
            'arguments': cron_job.args,
            'user_id': cron_job.user_id,
            'priority': cron_job.priority,
            'worker_process_id': worker_process_id,
        }
        
        return await cls.create(log_data)

    async def mark_success(self, result: str = None):
        """Mark the log entry as successful"""
        update_data = {
            'status': 'success',
            'end_time': datetime.now(),
            'result': str(result) if result is not None else None,
        }
        await self.write(update_data)
        await self._compute_execution_time()

    async def mark_failed(self, error_message: str, error_traceback: str = None):
        """Mark the log entry as failed"""
        update_data = {
            'status': 'failed',
            'end_time': datetime.now(),
            'error_message': error_message,
            'error_traceback': error_traceback,
        }
        await self.write(update_data)
        await self._compute_execution_time()

    async def mark_timeout(self):
        """Mark the log entry as timed out"""
        update_data = {
            'status': 'timeout',
            'end_time': datetime.now(),
            'error_message': 'Job execution timed out',
        }
        await self.write(update_data)
        await self._compute_execution_time()

    async def mark_cancelled(self, reason: str = None):
        """Mark the log entry as cancelled"""
        update_data = {
            'status': 'cancelled',
            'end_time': datetime.now(),
            'error_message': reason or 'Job execution was cancelled',
        }
        await self.write(update_data)
        await self._compute_execution_time()

    @classmethod
    async def get_job_statistics(cls, cron_id: str = None, days: int = 30) -> dict:
        """Get execution statistics for cron jobs"""
        domain = []
        
        if cron_id:
            domain.append(('cron_id', '=', cron_id))
        
        # Filter by date range
        cutoff_date = datetime.now() - timedelta(days=days)
        domain.append(('start_time', '>=', cutoff_date))
        
        logs = await cls.search(domain)
        
        total_executions = len(logs)
        successful = len([log for log in logs if log.status == 'success'])
        failed = len([log for log in logs if log.status == 'failed'])
        timeouts = len([log for log in logs if log.status == 'timeout'])
        
        # Calculate average execution time for successful jobs
        successful_logs = [log for log in logs if log.status == 'success' and log.execution_time > 0]
        avg_execution_time = sum(log.execution_time for log in successful_logs) / len(successful_logs) if successful_logs else 0
        
        return {
            'total_executions': total_executions,
            'successful': successful,
            'failed': failed,
            'timeouts': timeouts,
            'success_rate': (successful / total_executions * 100) if total_executions > 0 else 0,
            'average_execution_time': avg_execution_time,
        }

    @classmethod
    async def cleanup_old_logs(cls, max_age_hours: int = None):
        """Clean up old log entries beyond the configured retention period"""
        if max_age_hours is None:
            max_age_hours = cls._transient_max_hours
        
        logger = get_logger(f"{__name__}.{cls._name}")
        logger.debug(f"Running cleanup for {cls._name} with max age {max_age_hours} hours")

        # Calculate cutoff time
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        try:
            # Delete records older than cutoff time
            domain = [("start_time", "<", cutoff_time)]
            old_records = await cls.search(domain)

            if old_records:
                await old_records.unlink()
                logger.info(f"Cleaned up {len(old_records)} old cron log records")
        except Exception as e:
            logger.error(f"Error cleaning up old cron log records: {e}")

# The model is automatically registered via the metaclass
