{
    'name': 'Base',
    'version': '1.0.0',
    'description': 'Base module providing core functionality including partners, users, and groups',
    'author': 'ERP System',
    'category': 'Hidden',
    'depends': [],
    'data': [
        'data/ir_module_category_data.xml',  # Load categories first
        'data/res_groups_data.xml',          # Then groups that reference categories
        'data/res_users_data.xml',           # Then users that reference groups
        'data/res_country_data.xml',         # Country data (independent)
        'data/res_country_state_data.xml',   # Country states (depends on countries)
        'data/ir_job_channel_data.xml',      # Job channels for job execution
        'data/ir_cron_job_data.xml',         # Default cron jobs
    ],
    'installable': True,
    'auto_install': True,
}
